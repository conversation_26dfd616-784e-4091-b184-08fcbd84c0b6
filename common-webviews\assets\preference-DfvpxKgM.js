import{S as Q,i as V,s as X,n as L,d as S,P as $e,$ as P,c as W,e as r,R as F,F as h,V as b,h as c,X as se,Y as ie,ak as ue,aa as ge,D as E,t as M,q as B,o as re,p as ce,E as N,G as z,O as ve,ah as be,a3 as ee,a4 as te,a5 as ne,N as ye}from"./SpinnerAugment-VfHtkDdv.js";import{h as H,W as J}from"./IconButtonAugment-BlRCK7lJ.js";import{aJ as le}from"./AugmentMessage-C8cOeLWa.js";import{C as we,S as ke}from"./folder-opened-CgcyGshw.js";import{M as xe}from"./message-broker-DxXjuHCW.js";import{s as Ce}from"./chat-context-DhGlDJgc.js";import{M as qe}from"./index-BsnNYDaF.js";import"./CalloutAugment-jvmj3vIU.js";import"./CardAugment-CMpdst0l.js";import"./index-C5qylk65.js";import"./async-messaging-Cm1y2LK7.js";import"./types-CGlLNakm.js";import"./file-paths-CXmnYUii.js";import"./BaseTextInput-C9A3t790.js";import"./index-6WVCg-U8.js";import"./diff-operations-DfKvZ1Ug.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-JNVBjzXL.js";import"./keypress-DD1aQVr0.js";import"./await_block-CntY6A8u.js";import"./OpenFileButton-fgZNybO2.js";import"./index-B528snJk.js";import"./remote-agents-client-zf3VV9pT.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./TextAreaAugment-BnS2cUNC.js";import"./ButtonAugment-CRJIYorH.js";import"./CollapseButtonAugment-BcgZeyRI.js";import"./partner-mcp-utils-DbWhXw15.js";import"./MaterialIcon-YT2PSBkc.js";import"./CopyButton-BzMAWRcV.js";import"./copy-MzH1hy8q.js";import"./ellipsis-CQoYNkeK.js";import"./IconFilePath-qhm60SDK.js";import"./LanguageIcon-BXmH3Ek-.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-UFj4_Gis.js";import"./index-PzkfeRvH.js";import"./augment-logo-DHqqkJ4i.js";import"./pen-to-square-DiN4Ry3-.js";import"./chevron-down-DQi0HUpw.js";import"./check-ChePEq3H.js";function pe(s){let t,n;return{c(){t=h("div"),n=ie(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){W(e,t,i),r(t,n)},p(e,i){2&i&&se(n,e[1])},d(e){e&&S(t)}}}function Ae(s){let t,n,e,i,o,l,p,f,d,u,A,y,$,C,k,w,m,R,q=s[1]&&pe(s);return{c(){t=h("div"),q&&q.c(),n=b(),e=h("div"),i=h("button"),i.textContent="A",o=b(),l=h("button"),l.textContent="A",p=b(),f=h("button"),f.textContent="A",d=b(),u=h("button"),u.textContent="=",A=b(),y=h("button"),y.textContent="B",$=b(),C=h("button"),C.textContent="B",k=b(),w=h("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),P(i,"highlighted",s[0]==="A3"),c(l,"type","button"),c(l,"class","button medium svelte-1894wv4"),P(l,"highlighted",s[0]==="A2"),c(f,"type","button"),c(f,"class","button small svelte-1894wv4"),P(f,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),P(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),P(y,"highlighted",s[0]==="B1"),c(C,"type","button"),c(C,"class","button medium svelte-1894wv4"),P(C,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),P(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,D){W(g,t,D),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,o),r(e,l),r(e,p),r(e,f),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,C),r(e,k),r(e,w),m||(R=[F(i,"click",s[3]),F(l,"click",s[4]),F(f,"click",s[5]),F(u,"click",s[6]),F(y,"click",s[7]),F(C,"click",s[8]),F(w,"click",s[9])],m=!0)},p(g,[D]){g[1]?q?q.p(g,D):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&D&&P(i,"highlighted",g[0]==="A3"),1&D&&P(l,"highlighted",g[0]==="A2"),1&D&&P(f,"highlighted",g[0]==="A1"),1&D&&P(u,"highlighted",g[0]==="="),1&D&&P(y,"highlighted",g[0]==="B1"),1&D&&P(C,"highlighted",g[0]==="B2"),1&D&&P(w,"highlighted",g[0]==="B3")},i:L,o:L,d(g){g&&S(t),q&&q.d(),m=!1,$e(R)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function o(l){n(0,e=l)}return s.$$set=l=>{"selected"in l&&n(0,e=l.selected),"question"in l&&n(1,i=l.question)},[e,i,o,()=>o("A3"),()=>o("A2"),()=>o("A1"),()=>o("="),()=>o("B1"),()=>o("B2"),()=>o("B3")]}class ae extends Q{constructor(t){super(),V(this,t,Be,Ae,X,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=h("div"),n=ie(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){W(e,t,i),r(t,n)},p(e,i){2&i&&se(n,e[1])},d(e){e&&S(t)}}}function Me(s){let t,n,e,i,o,l=s[1]&&de(s);return{c(){t=h("div"),l&&l.c(),n=b(),e=h("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,f){W(p,t,f),l&&l.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(o=F(e,"input",s[3]),i=!0)},p(p,[f]){p[1]?l?l.p(p,f):(l=de(p),l.c(),l.m(t,n)):l&&(l.d(1),l=null),4&f&&c(e,"placeholder",p[2]),1&f&&ue(e,p[0])},i:L,o:L,d(p){p&&S(t),l&&l.d(),i=!1,o()}}}function Re(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:o=""}=t;return s.$$set=l=>{"value"in l&&n(0,e=l.value),"question"in l&&n(1,i=l.question),"placeholder"in l&&n(2,o=l.placeholder)},[e,i,o,function(){e=this.value,n(0,e)}]}class De extends Q{constructor(t){super(),V(this,t,Re,Me,X,{value:0,question:1,placeholder:2})}}function Se(s){let t,n,e,i;return{c(){t=h("button"),n=ie(s[0]),c(t,"class","button svelte-2k5n")},m(o,l){W(o,t,l),r(t,n),e||(i=F(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(o,[l]){s=o,1&l&&se(n,s[0])},i:L,o:L,d(o){o&&S(t),e=!1,i()}}}function We(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=o=>{"label"in o&&n(0,e=o.label),"onClick"in o&&n(1,i=o.onClick)},[e,i]}class Ie extends Q{constructor(t){super(),V(this,t,We,Se,X,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=h("div"),n=ie(s[1])},m(e,i){W(e,t,i),r(t,n)},p(e,i){2&i&&se(n,e[1])},d(e){e&&S(t)}}}function Oe(s){let t,n,e,i,o,l,p,f,d=s[1]&&me(s);return{c(){t=h("div"),d&&d.c(),n=b(),e=h("label"),i=h("input"),o=b(),l=h("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(l,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){W(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,o),r(e,l),p||(f=F(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:L,o:L,d(u){u&&S(t),d&&d.d(),p=!1,f()}}}function Pe(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=o=>{"isChecked"in o&&n(0,e=o.isChecked),"question"in o&&n(1,i=o.question)},[e,i,function(){e=this.checked,n(0,e)}]}class _e extends Q{constructor(t){super(),V(this,t,Pe,Oe,X,{isChecked:0,question:1})}}function Fe(s){let t;return{c(){t=h("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){W(n,t,e)},p:L,i:L,o:L,d(n){n&&S(t)}}}function Ee(s){let t,n,e,i,o,l,p,f,d,u,A,y,$,C,k,w,m;function R(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",R));let D={question:"Which response follows your instruction better?"};function Y(a){s[14](a)}s[3]!==void 0&&(D.selected=s[3]),i=new ae({props:D}),ee.push(()=>te(i,"selected",g));let K={question:"Which response is better overall?"};function I(a){s[15](a)}s[1]!==void 0&&(K.selected=s[1]),p=new ae({props:K}),ee.push(()=>te(p,"selected",Y));let O={question:s[9]};function j(a){s[16](a)}s[5]!==void 0&&(O.isChecked=s[5]),u=new _e({props:O}),ee.push(()=>te(u,"isChecked",I));let T={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(T.value=s[4]),$=new De({props:T}),ee.push(()=>te($,"value",j)),w=new Ie({props:{label:"Submit",onClick:s[10]}}),{c(){z(t.$$.fragment),e=b(),z(i.$$.fragment),l=b(),z(p.$$.fragment),d=b(),z(u.$$.fragment),y=b(),z($.$$.fragment),k=b(),z(w.$$.fragment)},m(a,x){N(t,a,x),W(a,e,x),N(i,a,x),W(a,l,x),N(p,a,x),W(a,d,x),N(u,a,x),W(a,y,x),N($,a,x),W(a,k,x),N(w,a,x),m=!0},p(a,x){const v={};!n&&4&x&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const _={};!o&&8&x&&(o=!0,_.selected=a[3],ne(()=>o=!1)),i.$set(_);const U={};!f&&2&x&&(f=!0,U.selected=a[1],ne(()=>f=!1)),p.$set(U);const G={};512&x&&(G.question=a[9]),!A&&32&x&&(A=!0,G.isChecked=a[5],ne(()=>A=!1)),u.$set(G);const Z={};!C&&16&x&&(C=!0,Z.value=a[4],ne(()=>C=!1)),$.$set(Z)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){M(t.$$.fragment,a),M(i.$$.fragment,a),M(p.$$.fragment,a),M(u.$$.fragment,a),M($.$$.fragment,a),M(w.$$.fragment,a),m=!1},d(a){a&&(S(e),S(l),S(d),S(y),S(k)),E(t,a),E(i,a),E(p,a),E(u,a),E($,a),E(w,a)}}}function Ne(s){let t,n,e,i,o,l,p,f,d,u,A,y,$,C,k,w,m,R,q,g,D,Y,K,I,O,j;o=new le({props:{markdown:s[0].data.a.message}}),$=new le({props:{markdown:s[8]}}),g=new le({props:{markdown:s[7]}});const T=[Ee,Fe],a=[];function x(v,_){return v[6]?0:1}return I=x(s),O=a[I]=T[I](s),{c(){t=h("main"),n=h("div"),e=h("h1"),e.textContent="Input message",i=b(),z(o.$$.fragment),l=b(),p=h("hr"),f=b(),d=h("div"),u=h("div"),A=h("h1"),A.textContent="Option A",y=b(),z($.$$.fragment),C=b(),k=h("div"),w=b(),m=h("div"),R=h("h1"),R.textContent="Option B",q=b(),z(g.$$.fragment),D=b(),Y=h("hr"),K=b(),O.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(R,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(Y,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,_){W(v,t,_),r(t,n),r(n,e),r(n,i),N(o,n,null),r(n,l),r(n,p),r(n,f),r(n,d),r(d,u),r(u,A),r(u,y),N($,u,null),r(d,C),r(d,k),r(d,w),r(d,m),r(m,R),r(m,q),N(g,m,null),r(n,D),r(n,Y),r(n,K),a[I].m(n,null),j=!0},p(v,[_]){const U={};1&_&&(U.markdown=v[0].data.a.message),o.$set(U);const G={};256&_&&(G.markdown=v[8]),$.$set(G);const Z={};128&_&&(Z.markdown=v[7]),g.$set(Z);let oe=I;I=x(v),I===oe?a[I].p(v,_):(re(),M(a[oe],1,1,()=>{a[oe]=null}),ce(),O=a[I],O?O.p(v,_):(O=a[I]=T[I](v),O.c()),B(O,1),O.m(n,null))},i(v){j||(B(o.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(O),j=!0)},o(v){M(o.$$.fragment,v),M($.$$.fragment,v),M(g.$$.fragment,v),M(O),j=!1},d(v){v&&S(t),E(o),E($),E(g),a[I].d()}}}function ze(s,t,n){let e,i,o,{inputData:l}=t;const p=ve();let f=new we(new xe(H),H,new ke);Ce(f);let d=null,u=null,A=null,y=null,$="",C=!1,k={a:null,b:null},w=l.data.a.response.length>0&&l.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const R=m.data;R.type===J.chatModelReply?(R.stream==="A"?n(11,k.a=R.data.text,k):R.stream==="B"&&n(11,k.b=R.data.text,k),n(11,k)):R.type===J.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,l=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:l.data.a.response),2049&s.$$.dirty&&n(7,o=k.b!==null?k.b:l.data.b.response),1&s.$$.dirty&&n(6,w=l.data.a.response.length>0&&l.data.b.response.length>0)},[l,y,d,u,$,C,w,o,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:C,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){C=m,n(5,C)},function(m){$=m,n(4,$)}]}class Le extends Q{constructor(t){super(),V(this,t,ze,Ne,X,{inputData:0})}}function he(s){let t,n,e=s[0].type==="Chat"&&fe(s);return{c(){e&&e.c(),t=ye()},m(i,o){e&&e.m(i,o),W(i,t,o),n=!0},p(i,o){i[0].type==="Chat"?e?(e.p(i,o),1&o&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),M(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){M(e),n=!1},d(i){i&&S(t),e&&e.d(i)}}}function fe(s){let t,n;return t=new Le({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){z(t.$$.fragment)},m(e,i){N(t,e,i),n=!0},p(e,i){const o={};1&i&&(o.inputData=e[0]),t.$set(o)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){M(t.$$.fragment,e),n=!1},d(e){E(t,e)}}}function He(s){let t,n,e=s[0]&&he(s);return{c(){t=h("main"),e&&e.c()},m(i,o){W(i,t,o),e&&e.m(t,null),n=!0},p(i,o){i[0]?e?(e.p(i,o),1&o&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),M(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){M(e),n=!1},d(i){i&&S(t),e&&e.d()}}}function je(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[He]},$$scope:{ctx:s}}}),{c(){z(t.$$.fragment)},m(o,l){N(t,o,l),n=!0,e||(i=F(window,"message",s[1]),e=!0)},p(o,[l]){const p={};17&l&&(p.$$scope={dirty:l,ctx:o}),t.$set(p)},i(o){n||(B(t.$$.fragment,o),n=!0)},o(o){M(t.$$.fragment,o),n=!1},d(o){E(t,o),e=!1,i()}}}function Ge(s,t,n){let e;return H.postMessage({type:J.preferencePanelLoaded}),[e,function(i){const o=i.data;o.type===J.preferenceInit&&n(0,e=o.data)},function(i){const o=i.detail;H.postMessage({type:J.preferenceResultMessage,data:o})},function(i){H.postMessage({type:J.preferenceNotify,data:i.detail})}]}class Je extends Q{constructor(t){super(),V(this,t,Ge,je,X,{})}}(async function(){H&&H.initialize&&await H.initialize(),new Je({target:document.getElementById("app")})})();
