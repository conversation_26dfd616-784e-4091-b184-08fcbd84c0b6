import{S,i as A,s as K,W as L,Z as x,a as v,d,D as M,t as $,q as u,_ as y,g as N,$ as p,c as f,E as V,F as g,G as W,a0 as b,I as Z,j as _,J as D,o as k,p as B,K as E,L as F,M as G,V as H,h as I}from"./SpinnerAugment-VfHtkDdv.js";const O=a=>({}),j=a=>({});function w(a){let c,o;const i=a[8].icon,l=D(i,a,a[9],j);return{c(){c=g("div"),l&&l.c(),I(c,"class","c-callout-icon svelte-1u5qnh6")},m(e,n){f(e,c,n),l&&l.m(c,null),o=!0},p(e,n){l&&l.p&&(!o||512&n)&&E(l,i,e,e[9],o?G(i,e[9],n,O):F(e[9]),j)},i(e){o||(u(l,e),o=!0)},o(e){$(l,e),o=!1},d(e){e&&d(c),l&&l.d(e)}}}function P(a){let c,o,i,l=a[7].icon&&w(a);const e=a[8].default,n=D(e,a,a[9],null);return{c(){l&&l.c(),c=H(),o=g("div"),n&&n.c(),I(o,"class","c-callout-body svelte-1u5qnh6")},m(t,s){l&&l.m(t,s),f(t,c,s),f(t,o,s),n&&n.m(o,null),i=!0},p(t,s){t[7].icon?l?(l.p(t,s),128&s&&u(l,1)):(l=w(t),l.c(),u(l,1),l.m(c.parentNode,c)):l&&(k(),$(l,1,1,()=>{l=null}),B()),n&&n.p&&(!i||512&s)&&E(n,e,t,t[9],i?G(e,t[9],s,null):F(t[9]),null)},i(t){i||(u(l),u(n,t),i=!0)},o(t){$(l),$(n,t),i=!1},d(t){t&&(d(c),d(o)),l&&l.d(t),n&&n.d(t)}}}function Q(a){let c,o,i,l;o=new L({props:{size:a[6],$$slots:{default:[P]},$$scope:{ctx:a}}});let e=[x(a[0]),{class:i=`c-callout c-callout--${a[0]} c-callout--${a[1]} c-callout--size-${a[2]} ${a[5]}`},a[4]],n={};for(let t=0;t<e.length;t+=1)n=v(n,e[t]);return{c(){c=g("div"),W(o.$$.fragment),y(c,n),p(c,"c-callout--highContrast",a[3]),p(c,"svelte-1u5qnh6",!0)},m(t,s){f(t,c,s),V(o,c,null),l=!0},p(t,[s]){const h={};640&s&&(h.$$scope={dirty:s,ctx:t}),o.$set(h),y(c,n=N(e,[1&s&&x(t[0]),(!l||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),p(c,"c-callout--highContrast",t[3]),p(c,"svelte-1u5qnh6",!0)},i(t){l||(u(o.$$.fragment,t),l=!0)},o(t){$(o.$$.fragment,t),l=!1},d(t){t&&d(c),M(o)}}}function R(a,c,o){let i,l;const e=["color","variant","size","highContrast"];let n=b(c,e),{$$slots:t={},$$scope:s}=c;const h=Z(t);let{color:z="info"}=c,{variant:C="soft"}=c,{size:m=2}=c,{highContrast:q=!1}=c;const J=m;return a.$$set=r=>{c=v(v({},c),_(r)),o(10,n=b(c,e)),"color"in r&&o(0,z=r.color),"variant"in r&&o(1,C=r.variant),"size"in r&&o(2,m=r.size),"highContrast"in r&&o(3,q=r.highContrast),"$$scope"in r&&o(9,s=r.$$scope)},a.$$.update=()=>{o(5,{class:i,...l}=n,i,(o(4,l),o(10,n)))},[z,C,m,q,l,i,J,h,t,s]}class U extends S{constructor(c){super(),A(this,c,R,Q,K,{color:0,variant:1,size:2,highContrast:3})}}export{U as C};
