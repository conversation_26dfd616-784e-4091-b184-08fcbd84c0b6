var r=(a=>(a[a.agentUnspecified=0]="agentUnspecified",a[a.agentPending=5]="agentPending",a[a.agentStarting=1]="agentStarting",a[a.agentRunning=2]="agentRunning",a[a.agentIdle=3]="agentIdle",a[a.agentFailed=4]="agentFailed",a))(r||{}),c=(a=>(a[a.workspaceUnspecified=0]="workspaceUnspecified",a[a.workspaceRunning=1]="workspaceRunning",a[a.workspacePausing=2]="workspacePausing",a[a.workspacePaused=3]="workspacePaused",a[a.workspaceResuming=4]="workspaceResuming",a))(c||{}),g=(a=>(a[a.AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED=0]="AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED",a[a.AGENT_HISTORY_EXCHANGE=1]="AGENT_HISTORY_EXCHANGE",a[a.AGENT_HISTORY_EXCHANGE_UPDATE=2]="AGENT_HISTORY_EXCHANGE_UPDATE",a[a.AGENT_HISTORY_AGENT_STATUS=3]="AGENT_HISTORY_AGENT_STATUS",a))(g||{}),l=(a=>(a[a.added=0]="added",a[a.deleted=1]="deleted",a[a.modified=2]="modified",a[a.renamed=3]="renamed",a))(l||{}),N=(a=>(a[a.unknown=0]="unknown",a[a.running=1]="running",a[a.success=2]="success",a[a.failure=3]="failure",a[a.skipped=4]="skipped",a))(N||{}),G=(a=>(a[a.AGENT_LIST_UPDATE_TYPE_UNSPECIFIED=0]="AGENT_LIST_UPDATE_TYPE_UNSPECIFIED",a[a.AGENT_LIST_AGENT_ADDED=1]="AGENT_LIST_AGENT_ADDED",a[a.AGENT_LIST_AGENT_UPDATED=2]="AGENT_LIST_AGENT_UPDATED",a[a.AGENT_LIST_AGENT_DELETED=3]="AGENT_LIST_AGENT_DELETED",a[a.AGENT_LIST_ALL_AGENTS=4]="AGENT_LIST_ALL_AGENTS",a))(G||{});function u(a){const T=new Set;return a&&a.length>0&&a.flatMap(n=>n.sections||[]).flatMap(n=>n.changes).forEach(n=>{T.add(n.path)}),T}function S(a,T,n={},_={}){const A=u(a),E=Array.from(A);E.length===0&&E.push(...T.map(t=>t.new_path||t.old_path).filter(Boolean));const e=E.every(t=>_[t]==="applied"),p=E.filter(t=>!_[t]||_[t]==="none"),i=[];return p.forEach(t=>{const d=a.flatMap(s=>s.sections||[]).flatMap(s=>s.changes).find(s=>s.path===t);if(d)i.push({path:t,originalCode:d.originalCode,newCode:n[t]||d.modifiedCode});else{const s=T.find(o=>(o.new_path||o.old_path)===t);s&&i.push({path:t,originalCode:s.old_contents||"",newCode:n[t]||s.new_contents||""})}}),{filesToApply:i,allPaths:E,areAllPathsApplied:e}}async function I(a,T){a.length&&T&&await Promise.all(a.map(async n=>await T(n.path,n.originalCode,n.newCode)))}function f(a){return a.sort((T,n)=>{const _=new Date(T.updated_at||T.started_at);return new Date(n.updated_at||n.started_at).getTime()-_.getTime()})}export{g as A,l as F,r as R,c as a,N as b,G as c,I as d,u as g,S as p,f as s};
