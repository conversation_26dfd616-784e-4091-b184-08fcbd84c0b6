import{_ as o,s as Te,g as qe,o as te,p as Ae,a as _e,b as Se,c as vt,l as _t,d as Zt,e as be,x as ke,G as D,a0 as Fe,i as Pe}from"./AugmentMessage-C8cOeLWa.js";import{l as Jt}from"./linear-porRNN_7.js";import"./SpinnerAugment-VfHtkDdv.js";import"./IconButtonAugment-BlRCK7lJ.js";import"./CalloutAugment-jvmj3vIU.js";import"./CardAugment-CMpdst0l.js";import"./index-C5qylk65.js";import"./async-messaging-Cm1y2LK7.js";import"./message-broker-DxXjuHCW.js";import"./types-CGlLNakm.js";import"./file-paths-CXmnYUii.js";import"./BaseTextInput-C9A3t790.js";import"./folder-opened-CgcyGshw.js";import"./index-6WVCg-U8.js";import"./diff-operations-DfKvZ1Ug.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-JNVBjzXL.js";import"./index-BsnNYDaF.js";import"./keypress-DD1aQVr0.js";import"./await_block-CntY6A8u.js";import"./OpenFileButton-fgZNybO2.js";import"./chat-context-DhGlDJgc.js";import"./index-B528snJk.js";import"./remote-agents-client-zf3VV9pT.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./TextAreaAugment-BnS2cUNC.js";import"./ButtonAugment-CRJIYorH.js";import"./CollapseButtonAugment-BcgZeyRI.js";import"./partner-mcp-utils-DbWhXw15.js";import"./MaterialIcon-YT2PSBkc.js";import"./CopyButton-BzMAWRcV.js";import"./copy-MzH1hy8q.js";import"./ellipsis-CQoYNkeK.js";import"./IconFilePath-qhm60SDK.js";import"./LanguageIcon-BXmH3Ek-.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-UFj4_Gis.js";import"./index-PzkfeRvH.js";import"./augment-logo-DHqqkJ4i.js";import"./pen-to-square-DiN4Ry3-.js";import"./chevron-down-DQi0HUpw.js";import"./check-ChePEq3H.js";import"./init-g68aIKmP.js";var Ct=function(){var t=o(function(r,_,g,d){for(g=g||{},d=r.length;d--;g[r[d]]=_);return g},"o"),n=[1,3],c=[1,4],h=[1,5],l=[1,6],x=[1,7],f=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],S=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],a=[55,56,57],m=[2,36],p=[1,37],y=[1,36],q=[1,38],T=[1,35],u=[1,43],A=[1,41],M=[1,14],Y=[1,23],j=[1,18],pt=[1,19],ct=[1,20],dt=[1,21],ut=[1,22],xt=[1,24],gt=[1,25],i=[1,26],Et=[1,27],Dt=[1,28],zt=[1,29],$=[1,32],U=[1,33],k=[1,34],F=[1,39],P=[1,40],C=[1,42],L=[1,44],O=[1,62],X=[1,61],v=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],It=[1,65],wt=[1,66],Nt=[1,67],Bt=[1,68],Wt=[1,69],Rt=[1,70],$t=[1,71],Ut=[1,72],Qt=[1,73],Ot=[1,74],Xt=[1,75],Ht=[1,76],w=[4,5,6,7,8,9,10,11,12,13,14,15,18],V=[1,90],Z=[1,91],J=[1,92],tt=[1,99],et=[1,93],it=[1,96],at=[1,94],nt=[1,95],rt=[1,97],st=[1,98],bt=[1,102],Mt=[10,55,56,57],B=[4,5,6,8,10,11,13,17,18,19,20,55,56,57],kt={trace:o(function(){},"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:o(function(r,_,g,d,b,e,ft){var s=e.length-1;switch(b){case 23:case 68:this.$=e[s];break;case 24:case 69:this.$=e[s-1]+""+e[s];break;case 26:this.$=e[s-1]+e[s];break;case 27:this.$=[e[s].trim()];break;case 28:e[s-2].push(e[s].trim()),this.$=e[s-2];break;case 29:this.$=e[s-4],d.addClass(e[s-2],e[s]);break;case 37:this.$=[];break;case 42:this.$=e[s].trim(),d.setDiagramTitle(this.$);break;case 43:this.$=e[s].trim(),d.setAccTitle(this.$);break;case 44:case 45:this.$=e[s].trim(),d.setAccDescription(this.$);break;case 46:d.addSection(e[s].substr(8)),this.$=e[s].substr(8);break;case 47:d.addPoint(e[s-3],"",e[s-1],e[s],[]);break;case 48:d.addPoint(e[s-4],e[s-3],e[s-1],e[s],[]);break;case 49:d.addPoint(e[s-4],"",e[s-2],e[s-1],e[s]);break;case 50:d.addPoint(e[s-5],e[s-4],e[s-2],e[s-1],e[s]);break;case 51:d.setXAxisLeftText(e[s-2]),d.setXAxisRightText(e[s]);break;case 52:e[s-1].text+=" ⟶ ",d.setXAxisLeftText(e[s-1]);break;case 53:d.setXAxisLeftText(e[s]);break;case 54:d.setYAxisBottomText(e[s-2]),d.setYAxisTopText(e[s]);break;case 55:e[s-1].text+=" ⟶ ",d.setYAxisBottomText(e[s-1]);break;case 56:d.setYAxisBottomText(e[s]);break;case 57:d.setQuadrant1Text(e[s]);break;case 58:d.setQuadrant2Text(e[s]);break;case 59:d.setQuadrant3Text(e[s]);break;case 60:d.setQuadrant4Text(e[s]);break;case 64:case 66:this.$={text:e[s],type:"text"};break;case 65:this.$={text:e[s-1].text+""+e[s],type:e[s-1].type};break;case 67:this.$={text:e[s],type:"markdown"}}},"anonymous"),table:[{18:n,26:1,27:2,28:c,55:h,56:l,57:x},{1:[3]},{18:n,26:8,27:2,28:c,55:h,56:l,57:x},{18:n,26:9,27:2,28:c,55:h,56:l,57:x},t(f,[2,33],{29:10}),t(S,[2,61]),t(S,[2,62]),t(S,[2,63]),{1:[2,30]},{1:[2,31]},t(a,m,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:p,5:y,10:q,12:T,13:u,14:A,18:M,25:Y,35:j,37:pt,39:ct,41:dt,42:ut,48:xt,50:gt,51:i,52:Et,53:Dt,54:zt,60:$,61:U,63:k,64:F,65:P,66:C,67:L}),t(f,[2,34]),{27:45,55:h,56:l,57:x},t(a,[2,37]),t(a,m,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:p,5:y,10:q,12:T,13:u,14:A,18:M,25:Y,35:j,37:pt,39:ct,41:dt,42:ut,48:xt,50:gt,51:i,52:Et,53:Dt,54:zt,60:$,61:U,63:k,64:F,65:P,66:C,67:L}),t(a,[2,39]),t(a,[2,40]),t(a,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(a,[2,45]),t(a,[2,46]),{18:[1,50]},{4:p,5:y,10:q,12:T,13:u,14:A,43:51,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,10:q,12:T,13:u,14:A,43:52,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,10:q,12:T,13:u,14:A,43:53,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,10:q,12:T,13:u,14:A,43:54,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,10:q,12:T,13:u,14:A,43:55,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,10:q,12:T,13:u,14:A,43:56,58:31,60:$,61:U,63:k,64:F,65:P,66:C,67:L},{4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,44:[1,57],47:[1,58],58:60,59:59,63:k,64:F,65:P,66:C,67:L},t(v,[2,64]),t(v,[2,66]),t(v,[2,67]),t(v,[2,70]),t(v,[2,71]),t(v,[2,72]),t(v,[2,73]),t(v,[2,74]),t(v,[2,75]),t(v,[2,76]),t(v,[2,77]),t(v,[2,78]),t(v,[2,79]),t(v,[2,80]),t(f,[2,35]),t(a,[2,38]),t(a,[2,42]),t(a,[2,43]),t(a,[2,44]),{3:64,4:It,5:wt,6:Nt,7:Bt,8:Wt,9:Rt,10:$t,11:Ut,12:Qt,13:Ot,14:Xt,15:Ht,21:63},t(a,[2,53],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,49:[1,77],63:k,64:F,65:P,66:C,67:L}),t(a,[2,56],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,49:[1,78],63:k,64:F,65:P,66:C,67:L}),t(a,[2,57],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),t(a,[2,58],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),t(a,[2,59],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),t(a,[2,60],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),{45:[1,79]},{44:[1,80]},t(v,[2,65]),t(v,[2,81]),t(v,[2,82]),t(v,[2,83]),{3:82,4:It,5:wt,6:Nt,7:Bt,8:Wt,9:Rt,10:$t,11:Ut,12:Qt,13:Ot,14:Xt,15:Ht,18:[1,81]},t(w,[2,23]),t(w,[2,1]),t(w,[2,2]),t(w,[2,3]),t(w,[2,4]),t(w,[2,5]),t(w,[2,6]),t(w,[2,7]),t(w,[2,8]),t(w,[2,9]),t(w,[2,10]),t(w,[2,11]),t(w,[2,12]),t(a,[2,52],{58:31,43:83,4:p,5:y,10:q,12:T,13:u,14:A,60:$,61:U,63:k,64:F,65:P,66:C,67:L}),t(a,[2,55],{58:31,43:84,4:p,5:y,10:q,12:T,13:u,14:A,60:$,61:U,63:k,64:F,65:P,66:C,67:L}),{46:[1,85]},{45:[1,86]},{4:V,5:Z,6:J,8:tt,11:et,13:it,16:89,17:at,18:nt,19:rt,20:st,22:88,23:87},t(w,[2,24]),t(a,[2,51],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),t(a,[2,54],{59:59,58:60,4:p,5:y,8:O,10:q,12:T,13:u,14:A,18:X,63:k,64:F,65:P,66:C,67:L}),t(a,[2,47],{22:88,16:89,23:100,4:V,5:Z,6:J,8:tt,11:et,13:it,17:at,18:nt,19:rt,20:st}),{46:[1,101]},t(a,[2,29],{10:bt}),t(Mt,[2,27],{16:103,4:V,5:Z,6:J,8:tt,11:et,13:it,17:at,18:nt,19:rt,20:st}),t(B,[2,25]),t(B,[2,13]),t(B,[2,14]),t(B,[2,15]),t(B,[2,16]),t(B,[2,17]),t(B,[2,18]),t(B,[2,19]),t(B,[2,20]),t(B,[2,21]),t(B,[2,22]),t(a,[2,49],{10:bt}),t(a,[2,48],{22:88,16:89,23:104,4:V,5:Z,6:J,8:tt,11:et,13:it,17:at,18:nt,19:rt,20:st}),{4:V,5:Z,6:J,8:tt,11:et,13:it,16:89,17:at,18:nt,19:rt,20:st,22:105},t(B,[2,26]),t(a,[2,50],{10:bt}),t(Mt,[2,28],{16:103,4:V,5:Z,6:J,8:tt,11:et,13:it,17:at,18:nt,19:rt,20:st})],defaultActions:{8:[2,30],9:[2,31]},parseError:o(function(r,_){if(!_.recoverable){var g=new Error(r);throw g.hash=_,g}this.trace(r)},"parseError"),parse:o(function(r){var _=this,g=[0],d=[],b=[null],e=[],ft=this.table,s="",mt=0,Yt=0,ye=e.slice.call(arguments,1),E=Object.create(this.lexer),G={yy:{}};for(var Ft in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Ft)&&(G.yy[Ft]=this.yy[Ft]);E.setInput(r,G.yy),G.yy.lexer=E,G.yy.parser=this,E.yylloc===void 0&&(E.yylloc={});var Pt=E.yylloc;e.push(Pt);var me=E.options&&E.options.ranges;function jt(){var R;return typeof(R=d.pop()||E.lex()||1)!="number"&&(R instanceof Array&&(R=(d=R).pop()),R=_.symbols_[R]||R),R}typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,o(function(R){g.length=g.length-2*R,b.length=b.length-R,e.length=e.length-R},"popStack"),o(jt,"lex");for(var N,K,W,Gt,Tt,H,Kt,qt,ot={};;){if(K=g[g.length-1],this.defaultActions[K]?W=this.defaultActions[K]:(N==null&&(N=jt()),W=ft[K]&&ft[K][N]),W===void 0||!W.length||!W[0]){var Vt="";for(Tt in qt=[],ft[K])this.terminals_[Tt]&&Tt>2&&qt.push("'"+this.terminals_[Tt]+"'");Vt=E.showPosition?"Parse error on line "+(mt+1)+`:
`+E.showPosition()+`
Expecting `+qt.join(", ")+", got '"+(this.terminals_[N]||N)+"'":"Parse error on line "+(mt+1)+": Unexpected "+(N==1?"end of input":"'"+(this.terminals_[N]||N)+"'"),this.parseError(Vt,{text:E.match,token:this.terminals_[N]||N,line:E.yylineno,loc:Pt,expected:qt})}if(W[0]instanceof Array&&W.length>1)throw new Error("Parse Error: multiple actions possible at state: "+K+", token: "+N);switch(W[0]){case 1:g.push(N),b.push(E.yytext),e.push(E.yylloc),g.push(W[1]),N=null,Yt=E.yyleng,s=E.yytext,mt=E.yylineno,Pt=E.yylloc;break;case 2:if(H=this.productions_[W[1]][1],ot.$=b[b.length-H],ot._$={first_line:e[e.length-(H||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(H||1)].first_column,last_column:e[e.length-1].last_column},me&&(ot._$.range=[e[e.length-(H||1)].range[0],e[e.length-1].range[1]]),(Gt=this.performAction.apply(ot,[s,Yt,mt,G.yy,W[1],b,e].concat(ye)))!==void 0)return Gt;H&&(g=g.slice(0,-1*H*2),b=b.slice(0,-1*H),e=e.slice(0,-1*H)),g.push(this.productions_[W[1]][0]),b.push(ot.$),e.push(ot._$),Kt=ft[g[g.length-2]][g[g.length-1]],g.push(Kt);break;case 3:return!0}}return!0},"parse")},pe=function(){return{EOF:1,parseError:o(function(r,_){if(!this.yy.parser)throw new Error(r);this.yy.parser.parseError(r,_)},"parseError"),setInput:o(function(r,_){return this.yy=_||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:o(function(){var r=this._input[0];return this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r,r.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:o(function(r){var _=r.length,g=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-_),this.offset-=_;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var b=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===d.length?this.yylloc.first_column:0)+d[d.length-g.length].length-g[0].length:this.yylloc.first_column-_},this.options.ranges&&(this.yylloc.range=[b[0],b[0]+this.yyleng-_]),this.yyleng=this.yytext.length,this},"unput"),more:o(function(){return this._more=!0,this},"more"),reject:o(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:o(function(r){this.unput(this.match.slice(r))},"less"),pastInput:o(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:o(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:o(function(){var r=this.pastInput(),_=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+_+"^"},"showPosition"),test_match:o(function(r,_){var g,d,b;if(this.options.backtrack_lexer&&(b={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(b.yylloc.range=this.yylloc.range.slice(0))),(d=r[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=d.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:d?d[d.length-1].length-d[d.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],g=this.performAction.call(this,this.yy,this,_,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var e in b)this[e]=b[e];return!1}return!1},"test_match"),next:o(function(){if(this.done)return this.EOF;var r,_,g,d;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var b=this._currentRules(),e=0;e<b.length;e++)if((g=this._input.match(this.rules[b[e]]))&&(!_||g[0].length>_[0].length)){if(_=g,d=e,this.options.backtrack_lexer){if((r=this.test_match(g,b[e]))!==!1)return r;if(this._backtrack){_=!1;continue}return!1}if(!this.options.flex)break}return _?(r=this.test_match(_,b[d]))!==!1&&r:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:o(function(){var r=this.next();return r||this.lex()},"lex"),begin:o(function(r){this.conditionStack.push(r)},"begin"),popState:o(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:o(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:o(function(r){return(r=this.conditionStack.length-1-Math.abs(r||0))>=0?this.conditionStack[r]:"INITIAL"},"topState"),pushState:o(function(r){this.begin(r)},"pushState"),stateStackSize:o(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:o(function(r,_,g,d){switch(g){case 0:case 1:case 3:break;case 2:return 55;case 4:return this.begin("title"),35;case 5:return this.popState(),"title_value";case 6:return this.begin("acc_title"),37;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),39;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:case 23:case 25:case 31:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 48;case 14:return 50;case 15:return 49;case 16:return 51;case 17:return 52;case 18:return 53;case 19:return 54;case 20:return 25;case 21:this.begin("md_string");break;case 22:return"MD_STR";case 24:this.begin("string");break;case 26:return"STR";case 27:this.begin("class_name");break;case 28:return this.popState(),47;case 29:return this.begin("point_start"),44;case 30:return this.begin("point_x"),45;case 32:this.popState(),this.begin("point_y");break;case 33:return this.popState(),46;case 34:return 28;case 35:return 4;case 36:return 11;case 37:return 64;case 38:return 10;case 39:case 40:return 65;case 41:return 14;case 42:return 13;case 43:return 67;case 44:return 66;case 45:return 12;case 46:return 8;case 47:return 5;case 48:return 18;case 49:return 56;case 50:return 63;case 51:return 57}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:!1},point_y:{rules:[33],inclusive:!1},point_x:{rules:[32],inclusive:!1},point_start:{rules:[30,31],inclusive:!1},acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},title:{rules:[5],inclusive:!1},md_string:{rules:[22,23],inclusive:!1},string:{rules:[25,26],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:!0}}}}();function yt(){this.yy={}}return kt.lexer=pe,o(yt,"Parser"),yt.prototype=kt,kt.Parser=yt,new yt}();Ct.parser=Ct;var Ce=Ct,I=Fe(),lt,Le=(lt=class{constructor(){this.classes=new Map,this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData()}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){var n,c,h,l,x,f,S,a,m,p,y,q,T,u,A,M,Y,j;return{showXAxis:!0,showYAxis:!0,showTitle:!0,chartHeight:((n=D.quadrantChart)==null?void 0:n.chartWidth)||500,chartWidth:((c=D.quadrantChart)==null?void 0:c.chartHeight)||500,titlePadding:((h=D.quadrantChart)==null?void 0:h.titlePadding)||10,titleFontSize:((l=D.quadrantChart)==null?void 0:l.titleFontSize)||20,quadrantPadding:((x=D.quadrantChart)==null?void 0:x.quadrantPadding)||5,xAxisLabelPadding:((f=D.quadrantChart)==null?void 0:f.xAxisLabelPadding)||5,yAxisLabelPadding:((S=D.quadrantChart)==null?void 0:S.yAxisLabelPadding)||5,xAxisLabelFontSize:((a=D.quadrantChart)==null?void 0:a.xAxisLabelFontSize)||16,yAxisLabelFontSize:((m=D.quadrantChart)==null?void 0:m.yAxisLabelFontSize)||16,quadrantLabelFontSize:((p=D.quadrantChart)==null?void 0:p.quadrantLabelFontSize)||16,quadrantTextTopPadding:((y=D.quadrantChart)==null?void 0:y.quadrantTextTopPadding)||5,pointTextPadding:((q=D.quadrantChart)==null?void 0:q.pointTextPadding)||5,pointLabelFontSize:((T=D.quadrantChart)==null?void 0:T.pointLabelFontSize)||12,pointRadius:((u=D.quadrantChart)==null?void 0:u.pointRadius)||5,xAxisPosition:((A=D.quadrantChart)==null?void 0:A.xAxisPosition)||"top",yAxisPosition:((M=D.quadrantChart)==null?void 0:M.yAxisPosition)||"left",quadrantInternalBorderStrokeWidth:((Y=D.quadrantChart)==null?void 0:Y.quadrantInternalBorderStrokeWidth)||1,quadrantExternalBorderStrokeWidth:((j=D.quadrantChart)==null?void 0:j.quadrantExternalBorderStrokeWidth)||2}}getDefaultThemeConfig(){return{quadrant1Fill:I.quadrant1Fill,quadrant2Fill:I.quadrant2Fill,quadrant3Fill:I.quadrant3Fill,quadrant4Fill:I.quadrant4Fill,quadrant1TextFill:I.quadrant1TextFill,quadrant2TextFill:I.quadrant2TextFill,quadrant3TextFill:I.quadrant3TextFill,quadrant4TextFill:I.quadrant4TextFill,quadrantPointFill:I.quadrantPointFill,quadrantPointTextFill:I.quadrantPointTextFill,quadrantXAxisTextFill:I.quadrantXAxisTextFill,quadrantYAxisTextFill:I.quadrantYAxisTextFill,quadrantTitleFill:I.quadrantTitleFill,quadrantInternalBorderStrokeFill:I.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:I.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData(),this.classes=new Map,_t.info("clear called")}setData(n){this.data={...this.data,...n}}addPoints(n){this.data.points=[...n,...this.data.points]}addClass(n,c){this.classes.set(n,c)}setConfig(n){_t.trace("setConfig called with: ",n),this.config={...this.config,...n}}setThemeConfig(n){_t.trace("setThemeConfig called with: ",n),this.themeConfig={...this.themeConfig,...n}}calculateSpace(n,c,h,l){const x=2*this.config.xAxisLabelPadding+this.config.xAxisLabelFontSize,f={top:n==="top"&&c?x:0,bottom:n==="bottom"&&c?x:0},S=2*this.config.yAxisLabelPadding+this.config.yAxisLabelFontSize,a={left:this.config.yAxisPosition==="left"&&h?S:0,right:this.config.yAxisPosition==="right"&&h?S:0},m=this.config.titleFontSize+2*this.config.titlePadding,p={top:l?m:0},y=this.config.quadrantPadding+a.left,q=this.config.quadrantPadding+f.top+p.top,T=this.config.chartWidth-2*this.config.quadrantPadding-a.left-a.right,u=this.config.chartHeight-2*this.config.quadrantPadding-f.top-f.bottom-p.top;return{xAxisSpace:f,yAxisSpace:a,titleSpace:p,quadrantSpace:{quadrantLeft:y,quadrantTop:q,quadrantWidth:T,quadrantHalfWidth:T/2,quadrantHeight:u,quadrantHalfHeight:u/2}}}getAxisLabels(n,c,h,l){const{quadrantSpace:x,titleSpace:f}=l,{quadrantHalfHeight:S,quadrantHeight:a,quadrantLeft:m,quadrantHalfWidth:p,quadrantTop:y,quadrantWidth:q}=x,T=!!this.data.xAxisRightText,u=!!this.data.yAxisTopText,A=[];return this.data.xAxisLeftText&&c&&A.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:m+(T?p/2:0),y:n==="top"?this.config.xAxisLabelPadding+f.top:this.config.xAxisLabelPadding+y+a+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:0}),this.data.xAxisRightText&&c&&A.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:m+p+(T?p/2:0),y:n==="top"?this.config.xAxisLabelPadding+f.top:this.config.xAxisLabelPadding+y+a+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:0}),this.data.yAxisBottomText&&h&&A.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+m+q+this.config.quadrantPadding,y:y+a-(u?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:-90}),this.data.yAxisTopText&&h&&A.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+m+q+this.config.quadrantPadding,y:y+S-(u?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:u?"center":"left",horizontalPos:"top",rotation:-90}),A}getQuadrants(n){const{quadrantSpace:c}=n,{quadrantHalfHeight:h,quadrantLeft:l,quadrantHalfWidth:x,quadrantTop:f}=c,S=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:l+x,y:f,width:x,height:h,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:l,y:f,width:x,height:h,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:l,y:f+h,width:x,height:h,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:l+x,y:f+h,width:x,height:h,fill:this.themeConfig.quadrant4Fill}];for(const a of S)a.text.x=a.x+a.width/2,this.data.points.length===0?(a.text.y=a.y+a.height/2,a.text.horizontalPos="middle"):(a.text.y=a.y+this.config.quadrantTextTopPadding,a.text.horizontalPos="top");return S}getQuadrantPoints(n){const{quadrantSpace:c}=n,{quadrantHeight:h,quadrantLeft:l,quadrantTop:x,quadrantWidth:f}=c,S=Jt().domain([0,1]).range([l,f+l]),a=Jt().domain([0,1]).range([h+x,x]);return this.data.points.map(m=>{const p=this.classes.get(m.className);return p&&(m={...p,...m}),{x:S(m.x),y:a(m.y),fill:m.color??this.themeConfig.quadrantPointFill,radius:m.radius??this.config.pointRadius,text:{text:m.text,fill:this.themeConfig.quadrantPointTextFill,x:S(m.x),y:a(m.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:m.strokeColor??this.themeConfig.quadrantPointFill,strokeWidth:m.strokeWidth??"0px"}})}getBorders(n){const c=this.config.quadrantExternalBorderStrokeWidth/2,{quadrantSpace:h}=n,{quadrantHalfHeight:l,quadrantHeight:x,quadrantLeft:f,quadrantHalfWidth:S,quadrantTop:a,quadrantWidth:m}=h;return[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:f-c,y1:a,x2:f+m+c,y2:a},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:f+m,y1:a+c,x2:f+m,y2:a+x-c},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:f-c,y1:a+x,x2:f+m+c,y2:a+x},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:f,y1:a+c,x2:f,y2:a+x-c},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:f+S,y1:a+c,x2:f+S,y2:a+x-c},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:f+c,y1:a+l,x2:f+m-c,y2:a+l}]}getTitle(n){if(n)return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}build(){const n=this.config.showXAxis&&!(!this.data.xAxisLeftText&&!this.data.xAxisRightText),c=this.config.showYAxis&&!(!this.data.yAxisTopText&&!this.data.yAxisBottomText),h=this.config.showTitle&&!!this.data.titleText,l=this.data.points.length>0?"bottom":this.config.xAxisPosition,x=this.calculateSpace(l,n,c,h);return{points:this.getQuadrantPoints(x),quadrants:this.getQuadrants(x),axisLabels:this.getAxisLabels(l,n,c,x),borderLines:this.getBorders(x),title:this.getTitle(h)}}},o(lt,"QuadrantBuilder"),lt),ht,At=(ht=class extends Error{constructor(n,c,h){super(`value for ${n} ${c} is invalid, please use a valid ${h}`),this.name="InvalidStyleError"}},o(ht,"InvalidStyleError"),ht);function Lt(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}function ee(t){return!/^\d+$/.test(t)}function ie(t){return!/^\d+px$/.test(t)}o(Lt,"validateHexCode"),o(ee,"validateNumber"),o(ie,"validateSizeInPixels");var ve=vt();function Q(t){return Pe(t.trim(),ve)}o(Q,"textSanitizer");var z=new Le;function ae(t){z.setData({quadrant1Text:Q(t.text)})}function ne(t){z.setData({quadrant2Text:Q(t.text)})}function re(t){z.setData({quadrant3Text:Q(t.text)})}function se(t){z.setData({quadrant4Text:Q(t.text)})}function oe(t){z.setData({xAxisLeftText:Q(t.text)})}function le(t){z.setData({xAxisRightText:Q(t.text)})}function he(t){z.setData({yAxisTopText:Q(t.text)})}function ce(t){z.setData({yAxisBottomText:Q(t.text)})}function St(t){const n={};for(const c of t){const[h,l]=c.trim().split(/\s*:\s*/);if(h==="radius"){if(ee(l))throw new At(h,l,"number");n.radius=parseInt(l)}else if(h==="color"){if(Lt(l))throw new At(h,l,"hex code");n.color=l}else if(h==="stroke-color"){if(Lt(l))throw new At(h,l,"hex code");n.strokeColor=l}else{if(h!=="stroke-width")throw new Error(`style named ${h} is not supported.`);if(ie(l))throw new At(h,l,"number of pixels (eg. 10px)");n.strokeWidth=l}}return n}function de(t,n,c,h,l){const x=St(l);z.addPoints([{x:c,y:h,text:Q(t.text),className:n,...x}])}function ue(t,n){z.addClass(t,St(n))}function xe(t){z.setConfig({chartWidth:t})}function ge(t){z.setConfig({chartHeight:t})}function fe(){const t=vt(),{themeVariables:n,quadrantChart:c}=t;return c&&z.setConfig(c),z.setThemeConfig({quadrant1Fill:n.quadrant1Fill,quadrant2Fill:n.quadrant2Fill,quadrant3Fill:n.quadrant3Fill,quadrant4Fill:n.quadrant4Fill,quadrant1TextFill:n.quadrant1TextFill,quadrant2TextFill:n.quadrant2TextFill,quadrant3TextFill:n.quadrant3TextFill,quadrant4TextFill:n.quadrant4TextFill,quadrantPointFill:n.quadrantPointFill,quadrantPointTextFill:n.quadrantPointTextFill,quadrantXAxisTextFill:n.quadrantXAxisTextFill,quadrantYAxisTextFill:n.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:n.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:n.quadrantInternalBorderStrokeFill,quadrantTitleFill:n.quadrantTitleFill}),z.setData({titleText:te()}),z.build()}o(ae,"setQuadrant1Text"),o(ne,"setQuadrant2Text"),o(re,"setQuadrant3Text"),o(se,"setQuadrant4Text"),o(oe,"setXAxisLeftText"),o(le,"setXAxisRightText"),o(he,"setYAxisTopText"),o(ce,"setYAxisBottomText"),o(St,"parseStyles"),o(de,"addPoint"),o(ue,"addClass"),o(xe,"setWidth"),o(ge,"setHeight"),o(fe,"getQuadrantData");var Ai={parser:Ce,db:{setWidth:xe,setHeight:ge,setQuadrant1Text:ae,setQuadrant2Text:ne,setQuadrant3Text:re,setQuadrant4Text:se,setXAxisLeftText:oe,setXAxisRightText:le,setYAxisTopText:he,setYAxisBottomText:ce,parseStyles:St,addPoint:de,addClass:ue,getQuadrantData:fe,clear:o(function(){z.clear(),ke()},"clear"),setAccTitle:Se,getAccTitle:_e,setDiagramTitle:Ae,getDiagramTitle:te,getAccDescription:qe,setAccDescription:Te},renderer:{draw:o((t,n,c,h)=>{var ut,xt,gt;function l(i){return i==="top"?"hanging":"middle"}function x(i){return i==="left"?"start":"middle"}function f(i){return`translate(${i.x}, ${i.y}) rotate(${i.rotation||0})`}o(l,"getDominantBaseLine"),o(x,"getTextAnchor"),o(f,"getTransformation");const S=vt();_t.debug(`Rendering quadrant chart
`+t);const a=S.securityLevel;let m;a==="sandbox"&&(m=Zt("#i"+n));const p=Zt(a==="sandbox"?m.nodes()[0].contentDocument.body:"body").select(`[id="${n}"]`),y=p.append("g").attr("class","main"),q=((ut=S.quadrantChart)==null?void 0:ut.chartWidth)??500,T=((xt=S.quadrantChart)==null?void 0:xt.chartHeight)??500;be(p,T,q,((gt=S.quadrantChart)==null?void 0:gt.useMaxWidth)??!0),p.attr("viewBox","0 0 "+q+" "+T),h.db.setHeight(T),h.db.setWidth(q);const u=h.db.getQuadrantData(),A=y.append("g").attr("class","quadrants"),M=y.append("g").attr("class","border"),Y=y.append("g").attr("class","data-points"),j=y.append("g").attr("class","labels"),pt=y.append("g").attr("class","title");u.title&&pt.append("text").attr("x",0).attr("y",0).attr("fill",u.title.fill).attr("font-size",u.title.fontSize).attr("dominant-baseline",l(u.title.horizontalPos)).attr("text-anchor",x(u.title.verticalPos)).attr("transform",f(u.title)).text(u.title.text),u.borderLines&&M.selectAll("line").data(u.borderLines).enter().append("line").attr("x1",i=>i.x1).attr("y1",i=>i.y1).attr("x2",i=>i.x2).attr("y2",i=>i.y2).style("stroke",i=>i.strokeFill).style("stroke-width",i=>i.strokeWidth);const ct=A.selectAll("g.quadrant").data(u.quadrants).enter().append("g").attr("class","quadrant");ct.append("rect").attr("x",i=>i.x).attr("y",i=>i.y).attr("width",i=>i.width).attr("height",i=>i.height).attr("fill",i=>i.fill),ct.append("text").attr("x",0).attr("y",0).attr("fill",i=>i.text.fill).attr("font-size",i=>i.text.fontSize).attr("dominant-baseline",i=>l(i.text.horizontalPos)).attr("text-anchor",i=>x(i.text.verticalPos)).attr("transform",i=>f(i.text)).text(i=>i.text.text),j.selectAll("g.label").data(u.axisLabels).enter().append("g").attr("class","label").append("text").attr("x",0).attr("y",0).text(i=>i.text).attr("fill",i=>i.fill).attr("font-size",i=>i.fontSize).attr("dominant-baseline",i=>l(i.horizontalPos)).attr("text-anchor",i=>x(i.verticalPos)).attr("transform",i=>f(i));const dt=Y.selectAll("g.data-point").data(u.points).enter().append("g").attr("class","data-point");dt.append("circle").attr("cx",i=>i.x).attr("cy",i=>i.y).attr("r",i=>i.radius).attr("fill",i=>i.fill).attr("stroke",i=>i.strokeColor).attr("stroke-width",i=>i.strokeWidth),dt.append("text").attr("x",0).attr("y",0).text(i=>i.text.text).attr("fill",i=>i.text.fill).attr("font-size",i=>i.text.fontSize).attr("dominant-baseline",i=>l(i.text.horizontalPos)).attr("text-anchor",i=>x(i.text.verticalPos)).attr("transform",i=>f(i.text))},"draw")},styles:o(()=>"","styles")};export{Ai as diagram};
