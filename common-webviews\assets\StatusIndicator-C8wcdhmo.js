var L=Object.defineProperty;var k=(a,t,s)=>t in a?L(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s;var A=(a,t,s)=>k(a,typeof t!="symbol"?t+"":t,s);import{W as f}from"./IconButtonAugment-BlRCK7lJ.js";import{S as m,i as b,s as S,a as y,n as v,d as g,b as M,g as W,u as Z,v as F,w as U,x as j,f as h,H as O,j as _,c as C,e as p,h as e,P as R,t as I,q as x,o as z,p as J,R as V,F as H,V as N,ai as $,aj as P,X as q,Y as T}from"./SpinnerAugment-VfHtkDdv.js";import{g as X}from"./remote-agents-client-zf3VV9pT.js";import{s as B}from"./index-PzkfeRvH.js";const nt="remoteAgentStore",ot="remoteAgentStore";function lt(a){const t=a;return Array.isArray(t==null?void 0:t.agentOverviews)&&Array.isArray(t==null?void 0:t.activeWebviews)&&((t==null?void 0:t.pinnedAgents)===void 0||typeof t.pinnedAgents=="object")}class dt{constructor(t,s=void 0,i,n){A(this,"subscribers",new Set);this._msgBroker=t,this._state=s,this.validateState=i,this._storeId=n,s&&this.setStateInternal(s)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,s){return t.id===this.storeId&&this.validateState(s)}update(t){const s=t(this._state);s!==void 0&&this.setStateInternal(s)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:f.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:f.getSharedWebviewState,id:this.storeId,data:{}});t.type===f.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case f.updateSharedWebviewState:case f.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}function Y(a){let t,s,i=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},a[0]],n={};for(let r=0;r<i.length;r+=1)n=y(n,i[r]);return{c(){t=h("svg"),s=new O(!0),this.h()},l(r){t=F(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=U(t);s=j(o,!0),o.forEach(g),this.h()},h(){s.a=null,M(t,n)},m(r,o){Z(r,t,o),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m134.6 51.7-10.8 140.9c-1.1 14.6-8.8 27.8-20.9 36-23.9 16.2-41.8 40.8-49.1 70.3l-1.3 5.1H168v-88c0-13.3 10.7-24 24-24s24 10.7 24 24v88h115.5l-1.3-5.1c-7.4-29.5-25.2-54.1-49.1-70.2-12.1-8.2-19.8-21.5-20.9-36l-10.8-141c-.1-1.2-.1-2.5-.1-3.7H134.8c0 1.2 0 2.5-.1 3.7zM168 352H32c-9.9 0-19.2-4.5-25.2-12.3s-8.2-17.9-5.8-27.5l6.2-25c10.3-41.3 35.4-75.7 68.7-98.3L83.1 96l3.7-48H56c-4.4 0-8.6-1.2-12.2-3.3C36.8 40.5 32 32.8 32 24 32 10.7 42.7 0 56 0h272c13.3 0 24 10.7 24 24 0 8.8-4.8 16.5-11.8 20.7-3.6 2.1-7.7 3.3-12.2 3.3h-30.8l3.7 48 7.1 92.9c33.3 22.6 58.4 57.1 68.7 98.3l6.2 25c2.4 9.6.2 19.7-5.8 27.5S361.7 352 351.9 352h-136v136c0 13.3-10.7 24-24 24s-24-10.7-24-24V352z"/>',t)},p(r,[o]){M(t,n=W(i,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&o&&r[0]]))},i:v,o:v,d(r){r&&g(t)}}}function D(a,t,s){return a.$$set=i=>{s(0,t=y(y({},t),_(i)))},[t=_(t)]}class ct extends m{constructor(t){super(),b(this,t,D,Y,S,{})}}function G(a){let t,s;return{c(){t=h("svg"),s=h("path"),e(s,"fill-rule","evenodd"),e(s,"clip-rule","evenodd"),e(s,"d","M5.5 1C5.22386 1 5 1.22386 5 1.5C5 1.77614 5.22386 2 5.5 2H9.5C9.77614 2 10 1.77614 10 1.5C10 1.22386 9.77614 1 9.5 1H5.5ZM3 3.5C3 3.22386 3.22386 3 3.5 3H5H10H11.5C11.7761 3 12 3.22386 12 3.5C12 3.77614 11.7761 4 11.5 4H11V12C11 12.5523 10.5523 13 10 13H5C4.44772 13 4 12.5523 4 12V4L3.5 4C3.22386 4 3 3.77614 3 3.5ZM5 4H10V12H5V4Z"),e(s,"fill","currentColor"),e(t,"width","15"),e(t,"height","15"),e(t,"viewBox","0 0 15 15"),e(t,"fill","none"),e(t,"xmlns","http://www.w3.org/2000/svg")},m(i,n){C(i,t,n),p(t,s)},p:v,i:v,o:v,d(i){i&&g(t)}}}class ut extends m{constructor(t){super(),b(this,t,null,G,S,{})}}function K(a){let t,s,i,n,r;return{c(){t=h("svg"),s=h("rect"),i=h("path"),n=h("path"),r=h("path"),e(s,"width","16"),e(s,"height","16"),e(s,"fill","currentColor"),e(s,"fill-opacity","0.01"),e(i,"fill-rule","evenodd"),e(i,"clip-rule","evenodd"),e(i,"d","M3.4718 3.46066C3.65925 3.2732 3.96317 3.2732 4.15062 3.46066L7.35062 6.66066C7.44064 6.75068 7.49121 6.87277 7.49121 7.00007C7.49121 7.12737 7.44064 7.24946 7.35062 7.33949L4.15062 10.5395C3.96317 10.7269 3.65925 10.7269 3.4718 10.5395C3.28435 10.352 3.28435 10.0481 3.4718 9.86067L6.33239 7.00007L3.4718 4.13949C3.28435 3.95203 3.28435 3.64812 3.4718 3.46066Z"),e(i,"fill","currentColor"),e(n,"fill-rule","evenodd"),e(n,"clip-rule","evenodd"),e(n,"d","M7.86854 10.6132C7.57399 10.6132 7.33521 10.8519 7.33521 11.1465C7.33521 11.441 7.57399 11.6798 7.86854 11.6798H12.1352C12.4298 11.6798 12.6685 11.441 12.6685 11.1465C12.6685 10.8519 12.4298 10.6132 12.1352 10.6132H7.86854Z"),e(n,"fill","currentColor"),e(r,"fill-rule","evenodd"),e(r,"clip-rule","evenodd"),e(r,"d","M2.13331 1.06665C1.5442 1.06665 1.06664 1.54421 1.06664 2.13332V13.8667C1.06664 14.4558 1.5442 14.9333 2.13331 14.9333H13.8667C14.4558 14.9333 14.9333 14.4558 14.9333 13.8667V2.13332C14.9333 1.54421 14.4558 1.06665 13.8667 1.06665H2.13331ZM2.13331 2.13332H13.8667V13.8667H2.13331V2.13332Z"),e(r,"fill","currentColor"),e(t,"width","16"),e(t,"height","16"),e(t,"viewBox","0 0 16 16"),e(t,"fill","none"),e(t,"xmlns","http://www.w3.org/2000/svg")},m(o,u){C(o,t,u),p(t,s),p(t,i),p(t,n),p(t,r)},p:v,i:v,o:v,d(o){o&&g(t)}}}class ht extends m{constructor(t){super(),b(this,t,null,K,S,{})}}function E(a){let t,s,i,n,r;return{c(){t=H("div"),s=T(a[1]),e(t,"class",i="status-label status-label--"+a[2]+" svelte-11be6ut")},m(o,u){C(o,t,u),p(t,s),r=!0},p(o,u){(!r||2&u)&&q(s,o[1]),(!r||4&u&&i!==(i="status-label status-label--"+o[2]+" svelte-11be6ut"))&&e(t,"class",i)},i(o){r||(o&&P(()=>{r&&(n||(n=$(t,B,{duration:100,axis:"x"},!0)),n.run(1))}),r=!0)},o(o){o&&(n||(n=$(t,B,{duration:100,axis:"x"},!1)),n.run(0)),r=!1},d(o){o&&g(t),o&&n&&n.end()}}}function Q(a){let t,s,i,n,r,o,u,w,l=a[3]&&E(a);return{c(){t=H("div"),s=H("div"),n=N(),l&&l.c(),e(s,"class",i="status-dot status-dot--"+a[2]+" svelte-11be6ut"),e(t,"class","status-indicator-container svelte-11be6ut"),e(t,"role","status"),e(t,"aria-label",r="Agent status: "+a[1]),e(t,"title",o="Status: "+a[1])},m(d,c){C(d,t,c),p(t,s),p(t,n),l&&l.m(t,null),u||(w=[V(t,"mouseenter",a[8]),V(t,"mouseleave",a[9])],u=!0)},p(d,[c]){4&c&&i!==(i="status-dot status-dot--"+d[2]+" svelte-11be6ut")&&e(s,"class",i),d[3]?l?(l.p(d,c),8&c&&x(l,1)):(l=E(d),l.c(),x(l,1),l.m(t,null)):l&&(z(),I(l,1,1,()=>{l=null}),J()),2&c&&r!==(r="Agent status: "+d[1])&&e(t,"aria-label",r),2&c&&o!==(o="Status: "+d[1])&&e(t,"title",o)},i(d){x(l)},o(d){I(l)},d(d){d&&g(t),l&&l.d(),u=!1,R(w)}}}function tt(a,t,s){let i,n,r,{status:o}=t,{workspaceStatus:u}=t,{isExpanded:w=!1}=t,{hasUpdates:l=!1}=t,d=!1;return a.$$set=c=>{"status"in c&&s(4,o=c.status),"workspaceStatus"in c&&s(5,u=c.workspaceStatus),"isExpanded"in c&&s(6,w=c.isExpanded),"hasUpdates"in c&&s(7,l=c.hasUpdates)},a.$$.update=()=>{65&a.$$.dirty&&s(3,i=w||d),178&a.$$.dirty&&(s(1,n=X(o,u,l)),s(2,r=n.toString()))},[d,n,r,i,o,u,w,l,()=>s(0,d=!0),()=>s(0,d=!1)]}class pt extends m{constructor(t){super(),b(this,t,tt,Q,S,{status:4,workspaceStatus:5,isExpanded:6,hasUpdates:7})}}export{ot as S,ht as T,pt as a,ut as b,ct as c,dt as d,nt as e,lt as v};
