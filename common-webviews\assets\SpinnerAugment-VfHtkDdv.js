var Wt=Object.defineProperty;var Gt=(t,n,e)=>n in t?Wt(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e;var A=(t,n,e)=>Gt(t,typeof n!="symbol"?n+"":n,e);function $(){}(function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))n(e);new MutationObserver(e=>{for(const o of e)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&n(r)}).observe(document,{childList:!0,subtree:!0})}function n(e){if(e.ep)return;e.ep=!0;const o=function(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}(e);fetch(e.href,o)}})();const ct=t=>t;function Q(t,n){for(const e in n)t[e]=n[e];return t}function wn(t){return!!t&&(typeof t=="object"||typeof t=="function")&&typeof t.then=="function"}function xt(t){return t()}function mt(){return Object.create(null)}function k(t){t.forEach(xt)}function M(t){return typeof t=="function"}function at(t,n){return t!=t?n==n:t!==n||t&&typeof t=="object"||typeof t=="function"}let W;function xn(t,n){return t===n||(W||(W=document.createElement("a")),W.href=n,t===W.href)}function lt(t,...n){if(t==null){for(const o of n)o(void 0);return $}const e=t.subscribe(...n);return e.unsubscribe?()=>e.unsubscribe():e}function En(t){let n;return lt(t,e=>n=e)(),n}function An(t,n,e){t.$$.on_destroy.push(lt(n,e))}function Kt(t,n,e,o){if(t){const r=Et(t,n,e,o);return t[0](r)}}function Et(t,n,e,o){return t[1]&&o?Q(e.ctx.slice(),t[1](o(n))):e.ctx}function Jt(t,n,e,o){if(t[2]&&o){const r=t[2](o(e));if(n.dirty===void 0)return r;if(typeof r=="object"){const i=[],s=Math.max(n.dirty.length,r.length);for(let a=0;a<s;a+=1)i[a]=n.dirty[a]|r[a];return i}return n.dirty|r}return n.dirty}function Qt(t,n,e,o,r,i){if(r){const s=Et(n,e,o,i);t.p(s,r)}}function Ut(t){if(t.ctx.length>32){const n=[],e=t.ctx.length/32;for(let o=0;o<e;o++)n[o]=-1;return n}return-1}function Vt(t){const n={};for(const e in t)e[0]!=="$"&&(n[e]=t[e]);return n}function gt(t,n){const e={};n=new Set(n);for(const o in t)n.has(o)||o[0]==="$"||(e[o]=t[o]);return e}function Nn(t){const n={};for(const e in t)n[e]=!0;return n}function kn(t){return t??""}function zn(t,n,e){return t.set(e),n}function Cn(t){return t&&M(t.destroy)?t.destroy:$}function Tn(t){const n=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return n?[parseFloat(n[1]),n[2]||"px"]:[t,"px"]}const At=typeof window<"u";let ut=At?()=>window.performance.now():()=>Date.now(),ft=At?t=>requestAnimationFrame(t):$;const j=new Set;function Nt(t){j.forEach(n=>{n.c(t)||(j.delete(n),n.f())}),j.size!==0&&ft(Nt)}function dt(t){let n;return j.size===0&&ft(Nt),{promise:new Promise(e=>{j.add(n={c:t,f:e})}),abort(){j.delete(n)}}}let U=!1;function Xt(t,n,e,o){for(;t<n;){const r=t+(n-t>>1);e(r)<=o?t=r+1:n=r}return t}function y(t,n){t.appendChild(n)}function kt(t){if(!t)return document;const n=t.getRootNode?t.getRootNode():t.ownerDocument;return n&&n.host?n:t.ownerDocument}function Yt(t){const n=w("style");return n.textContent="/* empty */",function(e,o){y(e.head||e,o),o.sheet}(kt(t),n),n.sheet}function Zt(t,n){if(U){for(function(e){if(e.hydrate_init)return;e.hydrate_init=!0;let o=e.childNodes;if(e.nodeName==="HEAD"){const l=[];for(let f=0;f<o.length;f++){const h=o[f];h.claim_order!==void 0&&l.push(h)}o=l}const r=new Int32Array(o.length+1),i=new Int32Array(o.length);r[0]=-1;let s=0;for(let l=0;l<o.length;l++){const f=o[l].claim_order,h=(s>0&&o[r[s]].claim_order<=f?s+1:Xt(1,s,p=>o[r[p]].claim_order,f))-1;i[l]=r[h]+1;const d=h+1;r[d]=l,s=Math.max(d,s)}const a=[],c=[];let u=o.length-1;for(let l=r[s]+1;l!=0;l=i[l-1]){for(a.push(o[l-1]);u>=l;u--)c.push(o[u]);u--}for(;u>=0;u--)c.push(o[u]);a.reverse(),c.sort((l,f)=>l.claim_order-f.claim_order);for(let l=0,f=0;l<c.length;l++){for(;f<a.length&&c[l].claim_order>=a[f].claim_order;)f++;const h=f<a.length?a[f]:null;e.insertBefore(c[l],h)}}(t),(t.actual_end_child===void 0||t.actual_end_child!==null&&t.actual_end_child.parentNode!==t)&&(t.actual_end_child=t.firstChild);t.actual_end_child!==null&&t.actual_end_child.claim_order===void 0;)t.actual_end_child=t.actual_end_child.nextSibling;n!==t.actual_end_child?n.claim_order===void 0&&n.parentNode===t||t.insertBefore(n,t.actual_end_child):t.actual_end_child=n.nextSibling}else n.parentNode===t&&n.nextSibling===null||t.appendChild(n)}function nt(t,n,e){t.insertBefore(n,e||null)}function tn(t,n,e){U&&!e?Zt(t,n):n.parentNode===t&&n.nextSibling==e||t.insertBefore(n,e||null)}function N(t){t.parentNode&&t.parentNode.removeChild(t)}function On(t,n){for(let e=0;e<t.length;e+=1)t[e]&&t[e].d(n)}function w(t){return document.createElement(t)}function zt(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Ct(t){return document.createTextNode(t)}function C(){return Ct(" ")}function nn(){return Ct("")}function _t(t,n,e,o){return t.addEventListener(n,e,o),()=>t.removeEventListener(n,e,o)}function Mn(t){return function(n){return n.preventDefault(),t.call(this,n)}}function Pn(t){return function(n){return n.stopPropagation(),t.call(this,n)}}function v(t,n,e){e==null?t.removeAttribute(n):t.getAttribute(n)!==e&&t.setAttribute(n,e)}const en=["width","height"];function rt(t,n){const e=Object.getOwnPropertyDescriptors(t.__proto__);for(const o in n)n[o]==null?t.removeAttribute(o):o==="style"?t.style.cssText=n[o]:o==="__value"?t.value=t[o]=n[o]:e[o]&&e[o].set&&en.indexOf(o)===-1?t[o]=n[o]:v(t,o,n[o])}function qn(t,n){for(const e in n)v(t,e,n[e])}function on(t,n){Object.keys(n).forEach(e=>{(function(o,r,i){const s=r.toLowerCase();s in o?o[s]=typeof o[s]=="boolean"&&i===""||i:r in o?o[r]=typeof o[r]=="boolean"&&i===""||i:v(o,r,i)})(t,e,n[e])})}function Ln(t){return/-/.test(t)?on:rt}function rn(t){return Array.from(t.childNodes)}function Tt(t){t.claim_info===void 0&&(t.claim_info={last_index:0,total_claimed:0})}function sn(t,n,e,o){return function(r,i,s,a,c=!1){Tt(r);const u=(()=>{for(let l=r.claim_info.last_index;l<r.length;l++){const f=r[l];if(i(f)){const h=s(f);return h===void 0?r.splice(l,1):r[l]=h,c||(r.claim_info.last_index=l),f}}for(let l=r.claim_info.last_index-1;l>=0;l--){const f=r[l];if(i(f)){const h=s(f);return h===void 0?r.splice(l,1):r[l]=h,c?h===void 0&&r.claim_info.last_index--:r.claim_info.last_index=l,f}}return a()})();return u.claim_order=r.claim_info.total_claimed,r.claim_info.total_claimed+=1,u}(t,r=>r.nodeName===n,r=>{const i=[];for(let s=0;s<r.attributes.length;s++){const a=r.attributes[s];e[a.name]||i.push(a.name)}i.forEach(s=>r.removeAttribute(s))},()=>o(n))}function Sn(t,n,e){return sn(t,n,e,zt)}function bt(t,n,e){for(let o=e;o<t.length;o+=1){const r=t[o];if(r.nodeType===8&&r.textContent.trim()===n)return o}return-1}function jn(t,n){const e=bt(t,"HTML_TAG_START",0),o=bt(t,"HTML_TAG_END",e+1);if(e===-1||o===-1)return new et(n);Tt(t);const r=t.splice(e,o-e+1);N(r[0]),N(r[r.length-1]);const i=r.slice(1,r.length-1);if(i.length===0)return new et(n);for(const s of i)s.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1;return new et(n,i)}function Dn(t,n){n=""+n,t.data!==n&&(t.data=n)}function Bn(t,n){t.value=n??""}function Rn(t,n,e,o){e==null?t.style.removeProperty(n):t.style.setProperty(n,e,"")}let G;function cn(){if(G===void 0){G=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{G=!0}}return G}function Fn(t,n){getComputedStyle(t).position==="static"&&(t.style.position="relative");const e=w("iframe");e.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),e.setAttribute("aria-hidden","true"),e.tabIndex=-1;const o=cn();let r;return o?(e.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",r=_t(window,"message",i=>{i.source===e.contentWindow&&n()})):(e.src="about:blank",e.onload=()=>{r=_t(e.contentWindow,"resize",n),n()}),y(t,e),()=>{(o||r&&e.contentWindow)&&r(),N(e)}}function S(t,n,e){t.classList.toggle(n,!!e)}function Ot(t,n,{bubbles:e=!1,cancelable:o=!1}={}){return new CustomEvent(t,{detail:n,bubbles:e,cancelable:o})}class an{constructor(n=!1){A(this,"is_svg",!1);A(this,"e");A(this,"n");A(this,"t");A(this,"a");this.is_svg=n,this.e=this.n=null}c(n){this.h(n)}m(n,e,o=null){this.e||(this.is_svg?this.e=zt(e.nodeName):this.e=w(e.nodeType===11?"TEMPLATE":e.nodeName),this.t=e.tagName!=="TEMPLATE"?e:e.content,this.c(n)),this.i(o)}h(n){this.e.innerHTML=n,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(n){for(let e=0;e<this.n.length;e+=1)nt(this.t,this.n[e],n)}p(n){this.d(),this.h(n),this.i(this.a)}d(){this.n.forEach(N)}}class et extends an{constructor(e=!1,o){super(e);A(this,"l");this.e=this.n=null,this.l=o}c(e){this.l?this.n=this.l:super.c(e)}i(e){for(let o=0;o<this.n.length;o+=1)tn(this.t,this.n[o],e)}}function In(t,n){return new t(n)}const V=new Map;let I,K=0;function X(t,n,e,o,r,i,s,a=0){const c=16.666/o;let u=`{
`;for(let g=0;g<=1;g+=c){const _=n+(e-n)*i(g);u+=100*g+`%{${s(_,1-_)}}
`}const l=u+`100% {${s(e,1-e)}}
}`,f=`__svelte_${function(g){let _=5381,b=g.length;for(;b--;)_=(_<<5)-_^g.charCodeAt(b);return _>>>0}(l)}_${a}`,h=kt(t),{stylesheet:d,rules:p}=V.get(h)||function(g,_){const b={stylesheet:Yt(_),rules:{}};return V.set(g,b),b}(h,t);p[f]||(p[f]=!0,d.insertRule(`@keyframes ${f} ${l}`,d.cssRules.length));const m=t.style.animation||"";return t.style.animation=`${m?`${m}, `:""}${f} ${o}ms linear ${r}ms 1 both`,K+=1,f}function Y(t,n){const e=(t.style.animation||"").split(", "),o=e.filter(n?i=>i.indexOf(n)<0:i=>i.indexOf("__svelte")===-1),r=e.length-o.length;r&&(t.style.animation=o.join(", "),K-=r,K||ft(()=>{K||(V.forEach(i=>{const{ownerNode:s}=i.stylesheet;s&&N(s)}),V.clear())}))}function F(t){I=t}function H(){if(!I)throw new Error("Function called outside component initialization");return I}function Hn(t){H().$$.on_mount.push(t)}function Wn(t){H().$$.on_destroy.push(t)}function Gn(){const t=H();return(n,e,{cancelable:o=!1}={})=>{const r=t.$$.callbacks[n];if(r){const i=Ot(n,e,{cancelable:o});return r.slice().forEach(s=>{s.call(t,i)}),!i.defaultPrevented}return!0}}function Kn(t,n){return H().$$.context.set(t,n),n}function Jn(t){return H().$$.context.get(t)}function Qn(t,n){const e=t.$$.callbacks[n.type];e&&e.slice().forEach(o=>o.call(this,n))}const L=[],yt=[];let D=[];const it=[],Mt=Promise.resolve();let st=!1;function Pt(){st||(st=!0,Mt.then(qt))}function Un(){return Pt(),Mt}function B(t){D.push(t)}function Vn(t){it.push(t)}const ot=new Set;let R,P=0;function qt(){if(P!==0)return;const t=I;do{try{for(;P<L.length;){const n=L[P];P++,F(n),ln(n.$$)}}catch(n){throw L.length=0,P=0,n}for(F(null),L.length=0,P=0;yt.length;)yt.pop()();for(let n=0;n<D.length;n+=1){const e=D[n];ot.has(e)||(ot.add(e),e())}D.length=0}while(L.length);for(;it.length;)it.pop()();st=!1,ot.clear(),F(t)}function ln(t){if(t.fragment!==null){t.update(),k(t.before_update);const n=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,n),t.after_update.forEach(B)}}function ht(){return R||(R=Promise.resolve(),R.then(()=>{R=null})),R}function T(t,n,e){t.dispatchEvent(Ot(`${n?"intro":"outro"}${e}`))}const J=new Set;let E;function Xn(){E={r:0,c:[],p:E}}function Yn(){E.r||k(E.c),E=E.p}function Lt(t,n){t&&t.i&&(J.delete(t),t.i(n))}function un(t,n,e,o){if(t&&t.o){if(J.has(t))return;J.add(t),E.c.push(()=>{J.delete(t),o&&(e&&t.d(1),o())}),t.o(n)}else o&&o()}const pt={duration:0};function Zn(t,n,e){const o={direction:"in"};let r,i,s=n(t,e,o),a=!1,c=0;function u(){r&&Y(t,r)}function l(){const{delay:h=0,duration:d=300,easing:p=ct,tick:m=$,css:g}=s||pt;g&&(r=X(t,0,1,d,h,p,g,c++)),m(0,1);const _=ut()+h,b=_+d;i&&i.abort(),a=!0,B(()=>T(t,!0,"start")),i=dt(x=>{if(a){if(x>=b)return m(1,0),T(t,!0,"end"),u(),a=!1;if(x>=_){const z=p((x-_)/d);m(z,1-z)}}return a})}let f=!1;return{start(){f||(f=!0,Y(t),M(s)?(s=s(o),ht().then(l)):l())},invalidate(){f=!1},end(){a&&(u(),a=!1)}}}function te(t,n,e){const o={direction:"out"};let r,i=n(t,e,o),s=!0;const a=E;let c;function u(){const{delay:l=0,duration:f=300,easing:h=ct,tick:d=$,css:p}=i||pt;p&&(r=X(t,1,0,f,l,h,p));const m=ut()+l,g=m+f;B(()=>T(t,!1,"start")),"inert"in t&&(c=t.inert,t.inert=!0),dt(_=>{if(s){if(_>=g)return d(0,1),T(t,!1,"end"),--a.r||k(a.c),!1;if(_>=m){const b=h((_-m)/f);d(1-b,b)}}return s})}return a.r+=1,M(i)?ht().then(()=>{i=i(o),u()}):u(),{end(l){l&&"inert"in t&&(t.inert=c),l&&i.tick&&i.tick(1,0),s&&(r&&Y(t,r),s=!1)}}}function ne(t,n,e,o){let r,i=n(t,e,{direction:"both"}),s=o?0:1,a=null,c=null,u=null;function l(){u&&Y(t,u)}function f(d,p){const m=d.b-s;return p*=Math.abs(m),{a:s,b:d.b,d:m,duration:p,start:d.start,end:d.start+p,group:d.group}}function h(d){const{delay:p=0,duration:m=300,easing:g=ct,tick:_=$,css:b}=i||pt,x={start:ut()+p,b:d};d||(x.group=E,E.r+=1),"inert"in t&&(d?r!==void 0&&(t.inert=r):(r=t.inert,t.inert=!0)),a||c?c=x:(b&&(l(),u=X(t,s,d,m,p,g,b)),d&&_(0,1),a=f(x,m),B(()=>T(t,d,"start")),dt(z=>{if(c&&z>c.start&&(a=f(c,m),c=null,T(t,a.b,"start"),b&&(l(),u=X(t,s,a.b,a.duration,0,g,i.css))),a){if(z>=a.end)_(s=a.b,1-s),T(t,a.b,"end"),c||(a.b?l():--a.group.r||k(a.group.c)),a=null;else if(z>=a.start){const Ht=z-a.start;s=a.a+a.d*g(Ht/a.duration),_(s,1-s)}}return!(!a&&!c)}))}return{run(d){M(i)?ht().then(()=>{i=i({direction:d?"in":"out"}),h(d)}):h(d)},end(){l(),a=c=null}}}function fn(t,n){const e={},o={},r={$$scope:1};let i=t.length;for(;i--;){const s=t[i],a=n[i];if(a){for(const c in s)c in a||(o[c]=1);for(const c in a)r[c]||(e[c]=a[c],r[c]=1);t[i]=a}else for(const c in s)r[c]=1}for(const s in o)s in e||(e[s]=void 0);return e}function ee(t){return typeof t=="object"&&t!==null?t:{}}function oe(t,n,e){const o=t.$$.props[n];o!==void 0&&(t.$$.bound[o]=e,e(t.$$.ctx[o]))}function re(t){t&&t.c()}function dn(t,n,e){const{fragment:o,after_update:r}=t.$$;o&&o.m(n,e),B(()=>{const i=t.$$.on_mount.map(xt).filter(M);t.$$.on_destroy?t.$$.on_destroy.push(...i):k(i),t.$$.on_mount=[]}),r.forEach(B)}function hn(t,n){const e=t.$$;e.fragment!==null&&(function(o){const r=[],i=[];D.forEach(s=>o.indexOf(s)===-1?r.push(s):i.push(s)),i.forEach(s=>s()),D=r}(e.after_update),k(e.on_destroy),e.fragment&&e.fragment.d(n),e.on_destroy=e.fragment=null,e.ctx=[])}function St(t,n,e,o,r,i,s=null,a=[-1]){const c=I;F(t);const u=t.$$={fragment:null,ctx:[],props:i,update:$,not_equal:r,bound:mt(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(n.context||(c?c.$$.context:[])),callbacks:mt(),dirty:a,skip_bound:!1,root:n.target||c.$$.root};s&&s(u.root);let l=!1;if(u.ctx=e?e(t,n.props||{},(f,h,...d)=>{const p=d.length?d[0]:h;return u.ctx&&r(u.ctx[f],u.ctx[f]=p)&&(!u.skip_bound&&u.bound[f]&&u.bound[f](p),l&&function(m,g){m.$$.dirty[0]===-1&&(L.push(m),Pt(),m.$$.dirty.fill(0)),m.$$.dirty[g/31|0]|=1<<g%31}(t,f)),h}):[],u.update(),l=!0,k(u.before_update),u.fragment=!!o&&o(u.ctx),n.target){if(n.hydrate){U=!0;const f=rn(n.target);u.fragment&&u.fragment.l(f),f.forEach(N)}else u.fragment&&u.fragment.c();n.intro&&Lt(t.$$.fragment),dn(t,n.target,n.anchor),U=!1,qt()}F(c)}class jt{constructor(){A(this,"$$");A(this,"$$set")}$destroy(){hn(this,1),this.$destroy=$}$on(n,e){if(!M(e))return $;const o=this.$$.callbacks[n]||(this.$$.callbacks[n]=[]);return o.push(e),()=>{const r=o.indexOf(e);r!==-1&&o.splice(r,1)}}$set(n){var e;this.$$set&&(e=n,Object.keys(e).length!==0)&&(this.$$.skip_bound=!0,this.$$set(n),this.$$.skip_bound=!1)}}const q=[];function pn(t,n){return{subscribe:Dt(t,n).subscribe}}function Dt(t,n=$){let e;const o=new Set;function r(s){if(at(t,s)&&(t=s,e)){const a=!q.length;for(const c of o)c[1](),q.push(c,t);if(a){for(let c=0;c<q.length;c+=2)q[c][0](q[c+1]);q.length=0}}}function i(s){r(s(t))}return{set:r,update:i,subscribe:function(s,a=$){const c=[s,a];return o.add(c),o.size===1&&(e=n(r,i)||$),s(t),()=>{o.delete(c),o.size===0&&e&&(e(),e=null)}}}}function ie(t,n,e){const o=!Array.isArray(t),r=o?[t]:t;if(!r.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=n.length<2;return pn(e,(s,a)=>{let c=!1;const u=[];let l=0,f=$;const h=()=>{if(l)return;f();const p=n(o?u[0]:u,s,a);i?s(p):f=M(p)?p:$},d=r.map((p,m)=>lt(p,g=>{u[m]=g,l&=~(1<<m),c&&h()},()=>{l|=1<<m}));return c=!0,h(),function(){k(d),f(),c=!1}})}function se(t){return{subscribe:t.subscribe.bind(t)}}let mn=document.documentElement;function O(){return mn??document.documentElement}var Bt=(t=>(t.light="light",t.dark="dark",t))(Bt||{}),Rt=(t=>(t.regular="regular",t.highContrast="high-contrast",t))(Rt||{});const Z="data-augment-theme-category",tt="data-augment-theme-intensity";function Ft(){const t=O().getAttribute(Z);if(t&&Object.values(Bt).includes(t))return t}function ce(t){t===void 0?O().removeAttribute(Z):O().setAttribute(Z,t)}function It(){const t=O().getAttribute(tt);if(t&&Object.values(Rt).includes(t))return t}function ae(t){t===void 0?O().removeAttribute(tt):O().setAttribute(tt,t)}const $t=Dt(void 0);function gn(t){const n=new MutationObserver(e=>{for(const o of e)if(o.type==="attributes"){t(Ft(),It());break}});return n.observe(O(),{attributeFilter:[Z,tt],attributes:!0}),n}gn((t,n)=>{$t.update(()=>({category:t,intensity:n}))}),$t.update(()=>({category:Ft(),intensity:It()})),typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add("4");var le=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ue(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function vt(t){return t?{"data-ds-color":t}:{}}function fe(t){return{"data-ds-radius":t}}function de(t,n,e){return e?{[`data-ds-${t}-${n}`]:!0,[`data-${n}`]:!0}:{}}function _n(t){let n,e,o;const r=t[7].default,i=Kt(r,t,t[6],null);let s=[t[3]?vt(t[3]):{},{class:e="c-text c-text--size-"+t[0]+" c-text--weight-"+t[1]+" c-text--type-"+t[2]+" c-text--color-"+t[3]+" "+t[5]},t[4]],a={};for(let c=0;c<s.length;c+=1)a=Q(a,s[c]);return{c(){n=w("span"),i&&i.c(),rt(n,a),S(n,"c-text--has-color",t[3]!==void 0),S(n,"svelte-bx0l0l",!0)},m(c,u){nt(c,n,u),i&&i.m(n,null),o=!0},p(c,[u]){i&&i.p&&(!o||64&u)&&Qt(i,r,c,c[6],o?Jt(r,c[6],u,null):Ut(c[6]),null),rt(n,a=fn(s,[8&u&&(c[3]?vt(c[3]):{}),(!o||47&u&&e!==(e="c-text c-text--size-"+c[0]+" c-text--weight-"+c[1]+" c-text--type-"+c[2]+" c-text--color-"+c[3]+" "+c[5]))&&{class:e},16&u&&c[4]])),S(n,"c-text--has-color",c[3]!==void 0),S(n,"svelte-bx0l0l",!0)},i(c){o||(Lt(i,c),o=!0)},o(c){un(i,c),o=!1},d(c){c&&N(n),i&&i.d(c)}}}function bn(t,n,e){let o,r;const i=["size","weight","type","color"];let s=gt(n,i),{$$slots:a={},$$scope:c}=n,{size:u=3}=n,{weight:l="regular"}=n,{type:f="default"}=n,{color:h}=n;return t.$$set=d=>{n=Q(Q({},n),Vt(d)),e(8,s=gt(n,i)),"size"in d&&e(0,u=d.size),"weight"in d&&e(1,l=d.weight),"type"in d&&e(2,f=d.type),"color"in d&&e(3,h=d.color),"$$scope"in d&&e(6,c=d.$$scope)},t.$$.update=()=>{e(5,{class:o,...r}=s,o,(e(4,r),e(8,s)))},[u,l,f,h,r,o,c,a]}class he extends jt{constructor(n){super(),St(this,n,bn,_n,at,{size:0,weight:1,type:2,color:3})}}function wt(t){let n,e,o,r,i,s,a,c,u,l,f,h,d,p,m,g,_;return{c(){n=w("div"),e=w("div"),o=C(),r=w("div"),i=C(),s=w("div"),a=C(),c=w("div"),u=C(),l=w("div"),f=C(),h=w("div"),d=C(),p=w("div"),m=C(),g=w("div"),v(e,"class","c-spinner__leaf svelte-abmqgo"),v(r,"class","c-spinner__leaf svelte-abmqgo"),v(s,"class","c-spinner__leaf svelte-abmqgo"),v(c,"class","c-spinner__leaf svelte-abmqgo"),v(l,"class","c-spinner__leaf svelte-abmqgo"),v(h,"class","c-spinner__leaf svelte-abmqgo"),v(p,"class","c-spinner__leaf svelte-abmqgo"),v(g,"class","c-spinner__leaf svelte-abmqgo"),v(n,"class",_="c-spinner c-spinner--size-"+t[0]+" "+t[3]+" svelte-abmqgo"),v(n,"data-testid","spinner-augment"),S(n,"c-spinner--current-color",t[2])},m(b,x){nt(b,n,x),y(n,e),y(n,o),y(n,r),y(n,i),y(n,s),y(n,a),y(n,c),y(n,u),y(n,l),y(n,f),y(n,h),y(n,d),y(n,p),y(n,m),y(n,g)},p(b,x){9&x&&_!==(_="c-spinner c-spinner--size-"+b[0]+" "+b[3]+" svelte-abmqgo")&&v(n,"class",_),13&x&&S(n,"c-spinner--current-color",b[2])},d(b){b&&N(n)}}}function yn(t){let n,e=t[1]&&wt(t);return{c(){e&&e.c(),n=nn()},m(o,r){e&&e.m(o,r),nt(o,n,r)},p(o,[r]){o[1]?e?e.p(o,r):(e=wt(o),e.c(),e.m(n.parentNode,n)):e&&(e.d(1),e=null)},i:$,o:$,d(o){o&&N(n),e&&e.d(o)}}}function $n(t,n,e){let{size:o=2}=n,{loading:r=!0}=n,{useCurrentColor:i=!1}=n,{class:s=""}=n;return t.$$set=a=>{"size"in a&&e(0,o=a.size),"loading"in a&&e(1,r=a.loading),"useCurrentColor"in a&&e(2,i=a.useCurrentColor),"class"in a&&e(3,s=a.class)},[o,r,i,s]}class pe extends jt{constructor(n){super(),St(this,n,$n,yn,at,{size:0,loading:1,useCurrentColor:2,class:3})}}export{S as $,Dt as A,le as B,ue as C,hn as D,dn as E,w as F,re as G,et as H,Nn as I,Kt as J,Qt as K,Ut as L,Jt as M,nn as N,Gn as O,k as P,Rn as Q,_t as R,jt as S,Pn as T,Qn as U,C as V,he as W,Dn as X,Ct as Y,vt as Z,rt as _,Q as a,gt as a0,Tn as a1,ct as a2,yt as a3,oe as a4,Vn as a5,lt as a6,On as a7,kn as a8,Ln as a9,Mn as aA,pn as aB,de as aC,Fn as aD,te as aE,Zn as aF,M as aa,gn as ab,Ft as ac,Bt as ad,ee as ae,Wn as af,Cn as ag,Hn as ah,ne as ai,B as aj,Bn as ak,An as al,$t as am,pe as an,ie as ao,En as ap,se as aq,Un as ar,Rt as as,ce as at,ae as au,fe as av,zn as aw,xn as ax,In as ay,an as az,qn as b,nt as c,N as d,y as e,zt as f,fn as g,v as h,St as i,Vt as j,wn as k,H as l,F as m,$ as n,Xn as o,Yn as p,Lt as q,qt as r,at as s,un as t,tn as u,Sn as v,rn as w,jn as x,Jn as y,Kn as z};
