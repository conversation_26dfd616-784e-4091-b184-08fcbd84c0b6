import{p as P}from"./chunk-TMUBEWPD-LmNliwJT.js";import{T as y,O,aF as E,_ as m,g as L,s as N,a as V,b as G,o as I,p as _,l as z,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-C8cOeLWa.js";import{p as Y}from"./gitGraph-YCYPL57B-D27fxjkY.js";import{d as B}from"./arc-Bo_B8ph1.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-VfHtkDdv.js";import"./IconButtonAugment-BlRCK7lJ.js";import"./CalloutAugment-jvmj3vIU.js";import"./CardAugment-CMpdst0l.js";import"./index-C5qylk65.js";import"./async-messaging-Cm1y2LK7.js";import"./message-broker-DxXjuHCW.js";import"./types-CGlLNakm.js";import"./file-paths-CXmnYUii.js";import"./BaseTextInput-C9A3t790.js";import"./folder-opened-CgcyGshw.js";import"./index-6WVCg-U8.js";import"./diff-operations-DfKvZ1Ug.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-JNVBjzXL.js";import"./index-BsnNYDaF.js";import"./keypress-DD1aQVr0.js";import"./await_block-CntY6A8u.js";import"./OpenFileButton-fgZNybO2.js";import"./chat-context-DhGlDJgc.js";import"./index-B528snJk.js";import"./remote-agents-client-zf3VV9pT.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./TextAreaAugment-BnS2cUNC.js";import"./ButtonAugment-CRJIYorH.js";import"./CollapseButtonAugment-BcgZeyRI.js";import"./partner-mcp-utils-DbWhXw15.js";import"./MaterialIcon-YT2PSBkc.js";import"./CopyButton-BzMAWRcV.js";import"./copy-MzH1hy8q.js";import"./ellipsis-CQoYNkeK.js";import"./IconFilePath-qhm60SDK.js";import"./LanguageIcon-BXmH3Ek-.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-UFj4_Gis.js";import"./index-PzkfeRvH.js";import"./augment-logo-DHqqkJ4i.js";import"./pen-to-square-DiN4Ry3-.js";import"./chevron-down-DQi0HUpw.js";import"./check-ChePEq3H.js";import"./_baseUniq-s71N3Uvp.js";import"./_basePickBy-JEPcOefk.js";import"./clone-Dn8BbtGd.js";import"./init-g68aIKmP.js";function tt(t,a){return a<t?-1:a>t?1:a>=t?0:NaN}function et(t){return t}var at=X.pie,R={sections:new Map,showData:!1},M=R.sections,F=R.showData,rt=structuredClone(at),W={getConfig:m(()=>structuredClone(rt),"getConfig"),clear:m(()=>{M=new Map,F=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:N,getAccDescription:L,addSection:m(({label:t,value:a})=>{M.has(t)||(M.set(t,a),z.debug(`added new section: ${t}, with value: ${a}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{F=t},"setShowData"),getShowData:m(()=>F,"getShowData")},it=m((t,a)=>{P(t,a),a.setShowData(t.showData),t.sections.map(a.addSection)},"populateDb"),nt={parse:m(async t=>{const a=await Y("pie",t);z.debug(a),it(a,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),st=m(t=>{const a=[...t.entries()].map(s=>({label:s[0],value:s[1]})).sort((s,u)=>u.value-s.value);return function(){var s=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function r(e){var i,l,n,T,g,p=(e=E(e)).length,v=0,A=new Array(p),d=new Array(p),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/p,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<p;++i)(g=d[A[i]=i]=+s(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-p*b)/v:0;i<p;++i,f=T)l=A[i],T=f+((g=d[l])>0?g*n:0)+b,d[l]={data:e[l],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return r.value=function(e){return arguments.length?(s=typeof e=="function"?e:y(+e),r):s},r.sortValues=function(e){return arguments.length?(u=e,c=null,r):u},r.sort=function(e){return arguments.length?(c=e,u=null,r):c},r.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),r):w},r.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),r):S},r.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),r):$},r}().value(s=>s.value)(a)},"createPieArcs"),ie={parser:nt,db:W,renderer:{draw:m((t,a,s,u)=>{z.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,r=450,e=r,i=J(a),l=i.append("g");l.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,p=Math.min(e,r)/2-40,v=B().innerRadius(0).outerRadius(p),A=B().innerRadius(p*g).outerRadius(p*g);l.append("circle").attr("cx",0).attr("cy",0).attr("r",p+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=st(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);l.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),l.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),l.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=l.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,r,D,S.useMaxWidth)},"draw")},styles:ot};export{ie as diagram};
