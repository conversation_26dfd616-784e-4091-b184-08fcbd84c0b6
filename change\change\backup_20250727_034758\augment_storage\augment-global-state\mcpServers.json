[{"name": "excel", "command": "npx --yes @negokaz/excel-mcp-server", "arguments": "", "useShellInterpolation": true, "env": {"EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"}, "id": "4a845aee-7843-472c-9fd7-eb7af860e0dc", "tools": [{"definition": {"name": "excel_copy_sheet_excel", "description": "Copy existing sheet to a new sheet", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"dstSheetName\":{\"description\":\"Sheet name to be copied\",\"type\":\"string\"},\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"srcSheetName\":{\"description\":\"Source sheet name in the Excel file\",\"type\":\"string\"}},\"required\":[\"fileAbsolutePath\",\"srcSheetName\",\"dstSheetName\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_copy_sheet"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_copy_sheet_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_create_table_excel", "description": "Create a table in the Excel sheet", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"range\":{\"description\":\"Range to be a table (e.g., \\\"A1:C10\\\")\",\"type\":\"string\"},\"sheetName\":{\"description\":\"Sheet name where the table is created\",\"type\":\"string\"},\"tableName\":{\"description\":\"Table name to be created\",\"type\":\"string\"}},\"required\":[\"fileAbsolutePath\",\"sheetName\",\"tableName\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_create_table"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_create_table_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_describe_sheets_excel", "description": "List all sheet information of specified Excel file", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"}},\"required\":[\"fileAbsolutePath\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_describe_sheets"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_describe_sheets_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_format_range_excel", "description": "Format cells in the Excel sheet with style information", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"range\":{\"description\":\"Range of cells in the Excel sheet (e.g., \\\"A1:C3\\\")\",\"type\":\"string\"},\"sheetName\":{\"description\":\"Sheet name in the Excel file\",\"type\":\"string\"},\"styles\":{\"description\":\"2D array of style objects for each cell. If a cell does not change style, use null. The number of items of the array must match the range size.\",\"items\":{\"items\":{\"anyOf\":[{\"description\":\"Style object for the cell\",\"properties\":{\"border\":{\"items\":{\"properties\":{\"color\":{\"pattern\":\"^#[0-9A-Fa-f]{6}$\",\"type\":\"string\"},\"style\":{\"enum\":[\"none\",\"continuous\",\"dash\",\"dot\",\"double\",\"dashDot\",\"dashDotDot\",\"slantDashDot\",\"mediumDashDot\",\"mediumDashDotDot\"],\"type\":\"string\"},\"type\":{\"enum\":[\"left\",\"right\",\"top\",\"bottom\",\"diagonalDown\",\"diagonalUp\"],\"type\":\"string\"}},\"required\":[\"type\"],\"type\":\"object\"},\"type\":\"array\"},\"decimalPlaces\":{\"maximum\":30,\"minimum\":0,\"type\":\"integer\"},\"fill\":{\"properties\":{\"color\":{\"items\":{\"pattern\":\"^#[0-9A-Fa-f]{6}$\",\"type\":\"string\"},\"type\":\"array\"},\"pattern\":{\"enum\":[\"none\",\"solid\",\"mediumGray\",\"darkGray\",\"lightGray\",\"darkHorizontal\",\"darkVertical\",\"darkDown\",\"darkUp\",\"darkGrid\",\"darkTrellis\",\"lightHorizontal\",\"lightVertical\",\"lightDown\",\"lightUp\",\"lightGrid\",\"lightTrellis\",\"gray125\",\"gray0625\"],\"type\":\"string\"},\"shading\":{\"enum\":[\"horizontal\",\"vertical\",\"diagonalDown\",\"diagonalUp\",\"fromCenter\",\"fromCorner\"],\"type\":\"string\"},\"type\":{\"enum\":[\"gradient\",\"pattern\"],\"type\":\"string\"}},\"required\":[\"type\",\"pattern\",\"color\"],\"type\":\"object\"},\"font\":{\"properties\":{\"bold\":{\"type\":\"boolean\"},\"color\":{\"pattern\":\"^#[0-9A-Fa-f]{6}$\",\"type\":\"string\"},\"italic\":{\"type\":\"boolean\"},\"size\":{\"maximum\":409,\"minimum\":1,\"type\":\"number\"},\"strike\":{\"type\":\"boolean\"},\"underline\":{\"enum\":[\"none\",\"single\",\"double\",\"singleAccounting\",\"doubleAccounting\"],\"type\":\"string\"},\"vertAlign\":{\"enum\":[\"baseline\",\"superscript\",\"subscript\"],\"type\":\"string\"}},\"type\":\"object\"},\"numFmt\":{\"description\":\"Custom number format string\",\"type\":\"string\"}},\"type\":\"object\"},{\"description\":\"No style applied to this cell\",\"type\":\"null\"}]},\"type\":\"array\"},\"type\":\"array\"}},\"required\":[\"fileAbsolutePath\",\"sheetName\",\"range\",\"styles\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_format_range"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_format_range_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_read_sheet_excel", "description": "Read values from Excel sheet with pagination.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"range\":{\"description\":\"Range of cells to read in the Excel sheet (e.g., \\\"A1:C10\\\"). [default: first paging range]\",\"type\":\"string\"},\"sheetName\":{\"description\":\"Sheet name in the Excel file\",\"type\":\"string\"},\"showFormula\":{\"description\":\"Show formula instead of value\",\"type\":\"boolean\"},\"showStyle\":{\"description\":\"Show style information for cells\",\"type\":\"boolean\"}},\"required\":[\"fileAbsolutePath\",\"sheetName\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_read_sheet"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_read_sheet_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_screen_capture_excel", "description": "[Windows only] Take a screenshot of the Excel sheet with pagination.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"range\":{\"description\":\"Range of cells to read in the Excel sheet (e.g., \\\"A1:C10\\\"). [default: first paging range]\",\"type\":\"string\"},\"sheetName\":{\"description\":\"Sheet name in the Excel file\",\"type\":\"string\"}},\"required\":[\"fileAbsolutePath\",\"sheetName\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_screen_capture"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_screen_capture_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "excel_write_to_sheet_excel", "description": "Write values to the Excel sheet", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"fileAbsolutePath\":{\"description\":\"Absolute path to the Excel file\",\"type\":\"string\"},\"newSheet\":{\"description\":\"Create a new sheet if true, otherwise write to the existing sheet\",\"type\":\"boolean\"},\"range\":{\"description\":\"Range of cells in the Excel sheet (e.g., \\\"A1:C10\\\")\",\"type\":\"string\"},\"sheetName\":{\"description\":\"Sheet name in the Excel file\",\"type\":\"string\"},\"values\":{\"description\":\"Values to write to the Excel sheet. If the value is a formula, it should start with \\\"=\\\"\",\"items\":{\"items\":{\"anyOf\":[{\"type\":\"string\"},{\"type\":\"number\"},{\"type\":\"boolean\"},{\"type\":\"null\"}]},\"type\":\"array\"},\"type\":\"array\"}},\"required\":[\"fileAbsolutePath\",\"sheetName\",\"newSheet\",\"range\",\"values\"]}", "tool_safety": 0, "original_mcp_server_name": "excel", "mcp_server_name": "excel", "mcp_tool_name": "excel_write_to_sheet"}, "identifier": {"hostName": "mcpHost", "toolId": "excel_write_to_sheet_excel"}, "isConfigured": true, "enabled": false, "toolSafety": 0}], "disabledTools": [], "disabled": true, "type": "stdio"}, {"name": "browsermcp", "command": "npx @browsermcp/mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "9c8cd6f3-c980-4078-877e-5e961cb038ee", "tools": [{"definition": {"name": "browser_navigate_browsermcp", "description": "Navigate to a URL", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to navigate to\"}},\"required\":[\"url\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_navigate"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_go_back_browsermcp", "description": "Go back to the previous page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_go_back"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_go_back_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_go_forward_browsermcp", "description": "Go forward to the next page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_go_forward"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_go_forward_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_snapshot_browsermcp", "description": "Capture accessibility snapshot of the current page. Use this for getting references to elements to interact with.", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_snapshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_snapshot_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_click_browsermcp", "description": "Perform click on a web page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_click"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_click_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_hover_browsermcp", "description": "Hover over element on page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_hover"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_hover_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_type_browsermcp", "description": "Type text into editable element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"text\":{\"type\":\"string\",\"description\":\"Text to type into the element\"},\"submit\":{\"type\":\"boolean\",\"description\":\"Whether to submit entered text (press Enter after)\"}},\"required\":[\"element\",\"ref\",\"text\",\"submit\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_type"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_type_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_select_option_browsermcp", "description": "Select an option in a dropdown", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"values\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"Array of values to select in the dropdown. This can be a single value or multiple values.\"}},\"required\":[\"element\",\"ref\",\"values\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_select_option"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_select_option_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_press_key_browsermcp", "description": "Press a key on the keyboard", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Name of the key to press or a character to generate, such as `ArrowLeft` or `a`\"}},\"required\":[\"key\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_press_key"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_press_key_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_wait_browsermcp", "description": "Wait for a specified time in seconds", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"time\":{\"type\":\"number\",\"description\":\"The time to wait in seconds\"}},\"required\":[\"time\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_wait"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_wait_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_get_console_logs_browsermcp", "description": "Get the console logs from the browser", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_get_console_logs"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_get_console_logs_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "browser_screenshot_browsermcp", "description": "Take a screenshot of the current page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "browsermcp", "mcp_server_name": "browsermcp", "mcp_tool_name": "browser_screenshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_screenshot_browsermcp"}, "isConfigured": true, "enabled": false, "toolSafety": 0}], "disabledTools": [], "disabled": true, "type": "stdio"}, {"type": "stdio", "name": "Playwright", "command": "npx -y @playwright/mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "376ea46f-92bd-4f40-bb1a-1c8be36b8c2e", "tools": [{"definition": {"name": "browser_close_Playwright", "description": "Close the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_close"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_close_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_resize_Playwright", "description": "Resize the browser window", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"width\":{\"type\":\"number\",\"description\":\"Width of the browser window\"},\"height\":{\"type\":\"number\",\"description\":\"Height of the browser window\"}},\"required\":[\"width\",\"height\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_resize"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_resize_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_console_messages_Playwright", "description": "Returns all console messages", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_console_messages"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_console_messages_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_handle_dialog_Playwright", "description": "Handle a dialog", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"accept\":{\"type\":\"boolean\",\"description\":\"Whether to accept the dialog.\"},\"promptText\":{\"type\":\"string\",\"description\":\"The text of the prompt in case of a prompt dialog.\"}},\"required\":[\"accept\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_handle_dialog"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_handle_dialog_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_evaluate_Playwright", "description": "Evaluate JavaScript expression on page or element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"function\":{\"type\":\"string\",\"description\":\"() => { /* code */ } or (element) => { /* code */ } when element is provided\"},\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"function\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_evaluate"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_evaluate_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_file_upload_Playwright", "description": "Upload one or multiple files", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"paths\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"The absolute paths to the files to upload. Can be a single file or multiple files.\"}},\"required\":[\"paths\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_file_upload"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_file_upload_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_install_Playwright", "description": "Install the browser specified in the config. Call this if you get an error about the browser not being installed.", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_install"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_install_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_press_key_Playwright", "description": "Press a key on the keyboard", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Name of the key to press or a character to generate, such as `ArrowLeft` or `a`\"}},\"required\":[\"key\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_press_key"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_press_key_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_type_Playwright", "description": "Type text into editable element", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"text\":{\"type\":\"string\",\"description\":\"Text to type into the element\"},\"submit\":{\"type\":\"boolean\",\"description\":\"Whether to submit entered text (press Enter after)\"},\"slowly\":{\"type\":\"boolean\",\"description\":\"Whether to type one character at a time. Useful for triggering key handlers in the page. By default entire text is filled in at once.\"}},\"required\":[\"element\",\"ref\",\"text\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_type"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_type_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_Playwright", "description": "Navigate to a URL", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to navigate to\"}},\"required\":[\"url\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_back_Playwright", "description": "Go back to the previous page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate_back"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_back_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_navigate_forward_Playwright", "description": "Go forward to the next page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_navigate_forward"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_navigate_forward_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_network_requests_Playwright", "description": "Returns all network requests since loading the page", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_network_requests"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_network_requests_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_take_screenshot_Playwright", "description": "Take a screenshot of the current page. You can't perform actions based on the screenshot, use browser_snapshot for actions.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"raw\":{\"type\":\"boolean\",\"description\":\"Whether to return without compression (in PNG format). Default is false, which returns a JPEG image.\"},\"filename\":{\"type\":\"string\",\"description\":\"File name to save the screenshot to. Defaults to `page-{timestamp}.{png|jpeg}` if not specified.\"},\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to screenshot the element. If not provided, the screenshot will be taken of viewport. If element is provided, ref must be provided too.\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot. If not provided, the screenshot will be taken of viewport. If ref is provided, element must be provided too.\"},\"fullPage\":{\"type\":\"boolean\",\"description\":\"When true, takes a screenshot of the full scrollable page, instead of the currently visible viewport. Cannot be used with element screenshots.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_take_screenshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_take_screenshot_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_snapshot_Playwright", "description": "Capture accessibility snapshot of the current page, this is better than screenshot", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_snapshot"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_snapshot_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_click_Playwright", "description": "Perform click on a web page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"doubleClick\":{\"type\":\"boolean\",\"description\":\"Whether to perform a double click instead of a single click\"},\"button\":{\"type\":\"string\",\"enum\":[\"left\",\"right\",\"middle\"],\"description\":\"Button to click, defaults to left\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_click"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_click_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_drag_Playwright", "description": "Perform drag and drop between two elements", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"startElement\":{\"type\":\"string\",\"description\":\"Human-readable source element description used to obtain the permission to interact with the element\"},\"startRef\":{\"type\":\"string\",\"description\":\"Exact source element reference from the page snapshot\"},\"endElement\":{\"type\":\"string\",\"description\":\"Human-readable target element description used to obtain the permission to interact with the element\"},\"endRef\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"startElement\",\"startRef\",\"endElement\",\"endRef\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_drag"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_drag_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_hover_Playwright", "description": "Hover over element on page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"}},\"required\":[\"element\",\"ref\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_hover"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_hover_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_select_option_Playwright", "description": "Select an option in a dropdown", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"element\":{\"type\":\"string\",\"description\":\"Human-readable element description used to obtain permission to interact with the element\"},\"ref\":{\"type\":\"string\",\"description\":\"Exact target element reference from the page snapshot\"},\"values\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"Array of values to select in the dropdown. This can be a single value or multiple values.\"}},\"required\":[\"element\",\"ref\",\"values\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_select_option"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_select_option_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_list_Playwright", "description": "List browser tabs", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_list"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_list_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_new_Playwright", "description": "Open a new tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"The URL to navigate to in the new tab. If not provided, the new tab will be blank.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_new"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_new_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_select_Playwright", "description": "Select a tab by index", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"index\":{\"type\":\"number\",\"description\":\"The index of the tab to select\"}},\"required\":[\"index\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_select"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_select_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_tab_close_Playwright", "description": "Close a tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"index\":{\"type\":\"number\",\"description\":\"The index of the tab to close. Closes current tab if not provided.\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_tab_close"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_tab_close_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "browser_wait_for_Playwright", "description": "Wait for text to appear or disappear or a specified time to pass", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"time\":{\"type\":\"number\",\"description\":\"The time to wait in seconds\"},\"text\":{\"type\":\"string\",\"description\":\"The text to wait for\"},\"textGone\":{\"type\":\"string\",\"description\":\"The text to wait for to disappear\"}},\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Playwright", "mcp_server_name": "Playwright", "mcp_tool_name": "browser_wait_for"}, "identifier": {"hostName": "mcpHost", "toolId": "browser_wait_for_Playwright"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabledTools": [], "disabled": false}, {"type": "stdio", "name": "Sequential thinking", "command": "npx -y @modelcontextprotocol/server-sequential-thinking", "arguments": "", "useShellInterpolation": true, "id": "1dac4a66-86a2-4032-b02f-afa7eac689c3", "tools": [{"definition": {"name": "sequentialthinking_Sequential_thinking", "description": "A detailed tool for dynamic and reflective problem-solving through thoughts.\nThis tool helps analyze problems through a flexible thinking process that can adapt and evolve.\nEach thought can build on, question, or revise previous insights as understanding deepens.\n\nWhen to use this tool:\n- Breaking down complex problems into steps\n- Planning and design with room for revision\n- Analysis that might need course correction\n- Problems where the full scope might not be clear initially\n- Problems that require a multi-step solution\n- Tasks that need to maintain context over multiple steps\n- Situations where irrelevant information needs to be filtered out\n\nKey features:\n- You can adjust total_thoughts up or down as you progress\n- You can question or revise previous thoughts\n- You can add more thoughts even after reaching what seemed like the end\n- You can express uncertainty and explore alternative approaches\n- Not every thought needs to build linearly - you can branch or backtrack\n- Generates a solution hypothesis\n- Verifies the hypothesis based on the Chain of Thought steps\n- Repeats the process until satisfied\n- Provides a correct answer\n\nParameters explained:\n- thought: Your current thinking step, which can include:\n* Regular analytical steps\n* Revisions of previous thoughts\n* Questions about previous decisions\n* Realizations about needing more analysis\n* Changes in approach\n* Hypothesis generation\n* Hypothesis verification\n- next_thought_needed: True if you need more thinking, even if at what seemed like the end\n- thought_number: Current number in sequence (can go beyond initial total if needed)\n- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)\n- is_revision: A boolean indicating if this thought revises previous thinking\n- revises_thought: If is_revision is true, which thought number is being reconsidered\n- branch_from_thought: If branching, which thought number is the branching point\n- branch_id: Identifier for the current branch (if any)\n- needs_more_thoughts: If reaching end but realizing more thoughts needed\n\nYou should:\n1. Start with an initial estimate of needed thoughts, but be ready to adjust\n2. Feel free to question or revise previous thoughts\n3. Don't hesitate to add more thoughts if needed, even at the \"end\"\n4. Express uncertainty when present\n5. Mark thoughts that revise previous thinking or branch into new paths\n6. Ignore information that is irrelevant to the current step\n7. Generate a solution hypothesis when appropriate\n8. Verify the hypothesis based on the Chain of Thought steps\n9. Repeat the process until satisfied with the solution\n10. Provide a single, ideally correct answer as the final output\n11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"thought\":{\"type\":\"string\",\"description\":\"Your current thinking step\"},\"nextThoughtNeeded\":{\"type\":\"boolean\",\"description\":\"Whether another thought step is needed\"},\"thoughtNumber\":{\"type\":\"integer\",\"description\":\"Current thought number\",\"minimum\":1},\"totalThoughts\":{\"type\":\"integer\",\"description\":\"Estimated total thoughts needed\",\"minimum\":1},\"isRevision\":{\"type\":\"boolean\",\"description\":\"Whether this revises previous thinking\"},\"revisesThought\":{\"type\":\"integer\",\"description\":\"Which thought is being reconsidered\",\"minimum\":1},\"branchFromThought\":{\"type\":\"integer\",\"description\":\"Branching point thought number\",\"minimum\":1},\"branchId\":{\"type\":\"string\",\"description\":\"Branch identifier\"},\"needsMoreThoughts\":{\"type\":\"boolean\",\"description\":\"If more thoughts are needed\"}},\"required\":[\"thought\",\"nextThoughtNeeded\",\"thoughtNumber\",\"totalThoughts\"]}", "tool_safety": 0, "original_mcp_server_name": "Sequential thinking", "mcp_server_name": "Sequential_thinking", "mcp_tool_name": "sequentialthinking"}, "identifier": {"hostName": "mcpHost", "toolId": "sequentialthinking_Sequential_thinking"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabledTools": [], "disabled": false}, {"type": "stdio", "name": "redis", "command": "npx -y @modelcontextprotocol/server-redis redis://localhost:6379", "arguments": "", "useShellInterpolation": true, "id": "bad24e78-c834-4385-b6a7-4d7387890ccf", "tools": [{"definition": {"name": "set_redis", "description": "Set a Redis key-value pair with optional expiration", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Redis key\"},\"value\":{\"type\":\"string\",\"description\":\"Value to store\"},\"expireSeconds\":{\"type\":\"number\",\"description\":\"Optional expiration time in seconds\"}},\"required\":[\"key\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "redis", "mcp_server_name": "redis", "mcp_tool_name": "set"}, "identifier": {"hostName": "mcpHost", "toolId": "set_redis"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get_redis", "description": "Get value by key from Redis", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"type\":\"string\",\"description\":\"Redis key to retrieve\"}},\"required\":[\"key\"]}", "tool_safety": 0, "original_mcp_server_name": "redis", "mcp_server_name": "redis", "mcp_tool_name": "get"}, "identifier": {"hostName": "mcpHost", "toolId": "get_redis"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "delete_redis", "description": "Delete one or more keys from Redis", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"key\":{\"oneOf\":[{\"type\":\"string\"},{\"type\":\"array\",\"items\":{\"type\":\"string\"}}],\"description\":\"Key or array of keys to delete\"}},\"required\":[\"key\"]}", "tool_safety": 0, "original_mcp_server_name": "redis", "mcp_server_name": "redis", "mcp_tool_name": "delete"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_redis"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_redis", "description": "List Redis keys matching a pattern", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"pattern\":{\"type\":\"string\",\"description\":\"Pattern to match keys (default: *)\"}}}", "tool_safety": 0, "original_mcp_server_name": "redis", "mcp_server_name": "redis", "mcp_tool_name": "list"}, "identifier": {"hostName": "mcpHost", "toolId": "list_redis"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabledTools": [], "disabled": false}, {"type": "stdio", "name": "feedback-md", "command": "npx feedback-md@latest", "arguments": "", "useShellInterpolation": true, "env": {"MCP_WEB_PORT": "5050"}, "id": "2cd96f74-d091-43e4-bb42-d083b2e8d35b", "tools": [{"definition": {"name": "feedback-md_feedback-md", "description": "Collect feedback from users about AI work summary. This tool opens a web interface for users to provide feedback on the AI's work.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"work_summary\":{\"type\":\"string\",\"description\":\"AI工作汇报内容，描述AI完成的工作和结果\"}},\"required\":[\"work_summary\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "feedback-md", "mcp_server_name": "feedback-md", "mcp_tool_name": "feedback-md"}, "identifier": {"hostName": "mcpHost", "toolId": "feedback-md_feedback-md"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "windows-cli", "command": "npx -y @simonb97/server-win-cli --config C:\\Users\\<USER>\\.win-cli-mcp\\config.json", "arguments": "", "useShellInterpolation": true, "id": "406f81bb-f3a6-4b15-adc5-072a6ab991e3", "tools": [{"definition": {"name": "execute_command_windows-cli", "description": "Execute a command in the specified shell (powershell, cmd, or gitbash)\n\nExample usage (PowerShell):\n```json\n{\n  \"shell\": \"powershell\",\n  \"command\": \"Get-Process | Select-Object -First 5\",\n  \"workingDir\": \"C:\\Users\\<USER>\\Projects\"\n}\n```\n\nExample usage (Git Bash):\n```json\n{\n  \"shell\": \"gitbash\",\n  \"command\": \"ls -la\",\n  \"workingDir\": \"/c/Users/<USER>\"\n}\n```", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"shell\":{\"type\":\"string\",\"enum\":[\"powershell\",\"cmd\",\"gitbash\"],\"description\":\"Shell to use for command execution\"},\"command\":{\"type\":\"string\",\"description\":\"Command to execute\"},\"workingDir\":{\"type\":\"string\",\"description\":\"Working directory for command execution (optional)\"}},\"required\":[\"shell\",\"command\"]}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "execute_command"}, "identifier": {"hostName": "mcpHost", "toolId": "execute_command_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "get_command_history_windows-cli", "description": "Get the history of executed commands\n\nExample usage:\n```json\n{\n  \"limit\": 5\n}\n```\n\nExample response:\n```json\n[\n  {\n    \"command\": \"Get-Process\",\n    \"output\": \"...\",\n    \"timestamp\": \"2024-03-20T10:30:00Z\",\n    \"exitCode\": 0\n  }\n]\n```", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"limit\":{\"type\":\"number\",\"description\":\"Maximum number of history entries to return (default: 10, max: 1000)\"}}}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "get_command_history"}, "identifier": {"hostName": "mcpHost", "toolId": "get_command_history_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "ssh_execute_windows-cli", "description": "Execute a command on a remote host via SSH\n\nExample usage:\n```json\n{\n  \"connectionId\": \"raspberry-pi\",\n  \"command\": \"uname -a\"\n}\n```\n\nConfiguration required in config.json:\n```json\n{\n  \"ssh\": {\n    \"enabled\": true,\n    \"connections\": {\n      \"raspberry-pi\": {\n        \"host\": \"raspberrypi.local\",\n        \"port\": 22,\n        \"username\": \"pi\",\n        \"password\": \"raspberry\"\n      }\n    }\n  }\n}\n```", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"connectionId\":{\"type\":\"string\",\"description\":\"ID of the SSH connection to use\",\"enum\":[\"tencent-cloud-server\"]},\"command\":{\"type\":\"string\",\"description\":\"Command to execute\"}},\"required\":[\"connectionId\",\"command\"]}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "ssh_execute"}, "identifier": {"hostName": "mcpHost", "toolId": "ssh_execute_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "ssh_disconnect_windows-cli", "description": "Disconnect from an SSH server\n\nExample usage:\n```json\n{\n  \"connectionId\": \"raspberry-pi\"\n}\n```\n\nUse this to cleanly close SSH connections when they're no longer needed.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"connectionId\":{\"type\":\"string\",\"description\":\"ID of the SSH connection to disconnect\",\"enum\":[\"tencent-cloud-server\"]}},\"required\":[\"connectionId\"]}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "ssh_disconnect"}, "identifier": {"hostName": "mcpHost", "toolId": "ssh_disconnect_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "create_ssh_connection_windows-cli", "description": "Create a new SSH connection", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"connectionId\":{\"type\":\"string\",\"description\":\"ID of the SSH connection\"},\"connectionConfig\":{\"type\":\"object\",\"properties\":{\"host\":{\"type\":\"string\",\"description\":\"Host of the SSH connection\"},\"port\":{\"type\":\"number\",\"description\":\"Port of the SSH connection\"},\"username\":{\"type\":\"string\",\"description\":\"Username for the SSH connection\"},\"password\":{\"type\":\"string\",\"description\":\"Password for the SSH connection\"},\"privateKeyPath\":{\"type\":\"string\",\"description\":\"Path to the private key for the SSH connection\"}},\"required\":[\"connectionId\",\"connectionConfig\"]}}}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "create_ssh_connection"}, "identifier": {"hostName": "mcpHost", "toolId": "create_ssh_connection_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "read_ssh_connections_windows-cli", "description": "Read all SSH connections", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "read_ssh_connections"}, "identifier": {"hostName": "mcpHost", "toolId": "read_ssh_connections_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "update_ssh_connection_windows-cli", "description": "Update an existing SSH connection", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"connectionId\":{\"type\":\"string\",\"description\":\"ID of the SSH connection to update\"},\"connectionConfig\":{\"type\":\"object\",\"properties\":{\"host\":{\"type\":\"string\",\"description\":\"Host of the SSH connection\"},\"port\":{\"type\":\"number\",\"description\":\"Port of the SSH connection\"},\"username\":{\"type\":\"string\",\"description\":\"Username for the SSH connection\"},\"password\":{\"type\":\"string\",\"description\":\"Password for the SSH connection\"},\"privateKeyPath\":{\"type\":\"string\",\"description\":\"Path to the private key for the SSH connection\"}},\"required\":[\"connectionId\",\"connectionConfig\"]}}}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "update_ssh_connection"}, "identifier": {"hostName": "mcpHost", "toolId": "update_ssh_connection_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "delete_ssh_connection_windows-cli", "description": "Delete an existing SSH connection", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"connectionId\":{\"type\":\"string\",\"description\":\"ID of the SSH connection to delete\"}},\"required\":[\"connectionId\"]}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "delete_ssh_connection"}, "identifier": {"hostName": "mcpHost", "toolId": "delete_ssh_connection_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}, {"definition": {"name": "get_current_directory_windows-cli", "description": "Get the current working directory", "input_schema_json": "{\"type\":\"object\",\"properties\":{}}", "tool_safety": 0, "original_mcp_server_name": "windows-cli", "mcp_server_name": "windows-cli", "mcp_tool_name": "get_current_directory"}, "identifier": {"hostName": "mcpHost", "toolId": "get_current_directory_windows-cli"}, "isConfigured": true, "enabled": false, "toolSafety": 0}], "disabled": true}, {"type": "stdio", "name": "Context 7", "command": "npx -y @upstash/context7-mcp@latest", "arguments": "", "useShellInterpolation": true, "id": "f9c88402-eeda-46c1-9fc4-59b205b901d3", "tools": [{"definition": {"name": "resolve-library-id_Context_7", "description": "Resolves a package/product name to a Context7-compatible library ID and returns a list of matching libraries.\n\nYou MUST call this function before 'get-library-docs' to obtain a valid Context7-compatible library ID UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.\n\nSelection Process:\n1. Analyze the query to understand what library/package the user is looking for\n2. Return the most relevant match based on:\n- Name similarity to the query (exact matches prioritized)\n- Description relevance to the query's intent\n- Documentation coverage (prioritize libraries with higher Code Snippet counts)\n- Trust score (consider libraries with scores of 7-10 more authoritative)\n\nResponse Format:\n- Return the selected library ID in a clearly marked section\n- Provide a brief explanation for why this library was chosen\n- If multiple good matches exist, acknowledge this but proceed with the most relevant one\n- If no good matches exist, clearly state this and suggest query refinements\n\nFor ambiguous queries, request clarification before proceeding with a best-guess match.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"libraryName\":{\"type\":\"string\",\"description\":\"Library name to search for and retrieve a Context7-compatible library ID.\"}},\"required\":[\"libraryName\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "resolve-library-id"}, "identifier": {"hostName": "mcpHost", "toolId": "resolve-library-id_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "get-library-docs_Context_7", "description": "Fetches up-to-date documentation for a library. You must call 'resolve-library-id' first to obtain the exact Context7-compatible library ID required to use this tool, UNLESS the user explicitly provides a library ID in the format '/org/project' or '/org/project/version' in their query.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"context7CompatibleLibraryID\":{\"type\":\"string\",\"description\":\"Exact Context7-compatible library ID (e.g., '/mongodb/docs', '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87') retrieved from 'resolve-library-id' or directly from user query in the format '/org/project' or '/org/project/version'.\"},\"topic\":{\"type\":\"string\",\"description\":\"Topic to focus documentation on (e.g., 'hooks', 'routing').\"},\"tokens\":{\"type\":\"number\",\"description\":\"Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens.\"}},\"required\":[\"context7CompatibleLibraryID\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}", "tool_safety": 0, "original_mcp_server_name": "Context 7", "mcp_server_name": "Context_7", "mcp_tool_name": "get-library-docs"}, "identifier": {"hostName": "mcpHost", "toolId": "get-library-docs_Context_7"}, "isConfigured": true, "enabled": true, "toolSafety": 0}], "disabled": false}, {"type": "stdio", "name": "mcp-jenkins", "command": "uvx mcp-jenkins --jenkins-url=https://test-parknew.lgfw24hours.com:3443/jenkins --jenkins-username=amin --jenkins-password=Lgjy2025testadmin!", "arguments": "", "useShellInterpolation": true, "id": "3f70f376-01fc-4790-8f8e-cdf0de6112e7", "tools": [], "disabled": true}, {"type": "stdio", "name": "AWS EC2 Pricing MCP", "command": "docker run --rm -i -q --network none ai1st/aws-pricing-mcp", "arguments": "", "useShellInterpolation": true, "id": "b80018e5-1918-419e-bfb5-3f215f34568e", "tools": [{"definition": {"name": "ec2_instances_pricing_AWS_EC2_Pricing_MCP", "description": "\nFind AWS EC2 instances based on specified criteria.\n\nFilter Parameters:\n- region: AWS region (default: us-east-1)\n- platform: OS platform (one of: Linux/UNIX, Red Hat Enterprise Linux, Red Hat Enterprise Linux with HA, Red Hat Enterprise Linux with SQL Server Standard and HA, Red Hat Enterprise Linux with SQL Server Enterprise and HA, Red Hat Enterprise Linux with SQL Server Standard, Red Hat Enterprise Linux with SQL Server Web, Linux with SQL Server Enterprise, Linux with SQL Server Standard, Linux with SQL Server Web, SUSE Linux, Windows, Windows BYOL, Windows with SQL Server Enterprise, Windows with SQL Server Standard, Windows with SQL Server Web; default: Linux/UNIX)\n- tenancy: Instance tenancy (one of: Shared, Dedicated; default: Shared)\n- pricing_model: Pricing model (one of: On Demand, 1-yr No Upfront, 1-yr Partial Upfront, 1-yr All Upfront, 3-yr No Upfront, 3-yr Partial Upfront, 3-yr All Upfront; default: On Demand)\n- min_vcpu: Minimum number of vCPUs (default: 0)\n- min_ram: Minimum amount of RAM in GB (default: 0)\n- min_gpu: Minimum number of GPUs (default: 0)\n- min_gpu_memory: Minimum GPU memory in GB (default: 0)\n- min_cpu_ghz: Minimum CPU clock speed in GHz (default: 0)\n- min_network_performance: Minimum network performance in Mbps (default: 0)\n- min_ebs_throughput: Minimum dedicated EBS throughput in Mbps (default: 0)\n- min_ephemeral_storage: Minimum ephemeral storage in GB (default: 0)\n- max_price_per_hour: Maximum price per hour in USD (default: no limit)\n- sort_by: Field to sort by (one of: Price, Clock Speed GHz, vCPU cores, Memory GB, Ephemeral Storage GB, Network Performance Mbps, Dedicated EBS Throughput Mbps, GPU cores, GPU Memory GB; default: Price)\n- sort_order: Sort order (one of: Ascending, Descending; default: Descending)\n- family: Filter by instance family (e.g., \"m5\", \"c6g\"; default: \"\" for all families)\n- size: Filter by instance size (e.g., \"large\", \"2xlarge\"; default: \"\" for all sizes)\n- processor: Filter by physical processor (e.g., \"Graviton\", \"Xeon\", \"AMD\"; default: \"\" for all processors)\n- page_num: Page number for pagination (default: 0)\n\nReturns:\n- List of instances matching the criteria (5 per page). CloudFix RightSpend pricing is provided when using the flexible cRIs provided by RightSpend (a third-party solution). The benefit of RightSpend is that it 1) eliminates the need for complex forecasting or frequent consultations with engineering about usage fluctuations 2) removes the risk of unused reservations 3) provides 3-yr All Upfront discounts without the need for prepayment.\n\n", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"filter_region\":{\"default\":\"us-east-1\",\"title\":\"Filter Region\",\"type\":\"string\"},\"filter_platform\":{\"default\":\"Linux/UNIX\",\"title\":\"Filter Platform\",\"type\":\"string\"},\"filter_tenancy\":{\"default\":\"Shared\",\"title\":\"Filter Tenancy\",\"type\":\"string\"},\"filter_pricing_model\":{\"default\":\"On Demand\",\"title\":\"Filter Pricing Model\",\"type\":\"string\"},\"filter_min_vcpu\":{\"default\":0,\"title\":\"Filter Min Vcpu\",\"type\":\"integer\"},\"filter_min_ram\":{\"default\":0,\"title\":\"Filter Min Ram\",\"type\":\"number\"},\"filter_min_gpu\":{\"default\":0,\"title\":\"Filter Min Gpu\",\"type\":\"integer\"},\"filter_min_gpu_memory\":{\"default\":0,\"title\":\"Filter Min Gpu Memory\",\"type\":\"integer\"},\"filter_min_cpu_ghz\":{\"default\":0,\"title\":\"Filter Min Cpu Ghz\",\"type\":\"number\"},\"filter_min_network_performance\":{\"default\":0,\"title\":\"Filter Min Network Performance\",\"type\":\"integer\"},\"filter_min_ebs_throughput\":{\"default\":0,\"title\":\"Filter Min Ebs Throughput\",\"type\":\"integer\"},\"filter_min_ephemeral_storage\":{\"default\":0,\"title\":\"Filter Min Ephemeral Storage\",\"type\":\"integer\"},\"filter_max_price_per_hour\":{\"default\":null,\"title\":\"Filter Max Price Per Hour\",\"type\":\"number\"},\"filter_family\":{\"default\":\"\",\"title\":\"Filter Family\",\"type\":\"string\"},\"filter_size\":{\"default\":\"\",\"title\":\"Filter Size\",\"type\":\"string\"},\"filter_processor\":{\"default\":\"\",\"title\":\"Filter Processor\",\"type\":\"string\"},\"sort_by\":{\"default\":\"Price\",\"title\":\"Sort By\",\"type\":\"string\"},\"sort_order\":{\"default\":\"Descending\",\"title\":\"Sort Order\",\"type\":\"string\"},\"page_num\":{\"default\":0,\"title\":\"Page Num\",\"type\":\"integer\"}},\"additionalProperties\":false}", "tool_safety": 0, "original_mcp_server_name": "AWS EC2 Pricing MCP", "mcp_server_name": "AWS_EC2_Pricing_MCP", "mcp_tool_name": "ec2_instances_pricing"}, "identifier": {"hostName": "mcpHost", "toolId": "ec2_instances_pricing_AWS_EC2_Pricing_MCP"}, "isConfigured": true, "enabled": false, "toolSafety": 0}], "disabled": true}, {"type": "stdio", "name": "mysql", "command": "uvx --from mysql-mcp-server mysql_mcp_server", "arguments": "", "useShellInterpolation": true, "env": {"MYSQL_HOST": "localhost", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "1234", "MYSQL_DATABASE": "nacos"}, "id": "a7902784-f560-4912-a79e-e1e116cb08f3"}]