{"remote.SSH.remotePlatform": {"AWS002": "linux", "AWStest001": "linux", "AWStest001-3": "linux", "AWStest002": "linux", "AWStest001-4": "linux", "AWStest003-J001": "linux"}, "augment.chat.userGuidelines": "1. Please reply to me in Chinese and use the context-aware function to retrieve the correct code file and code snippet part.\n2. When answering my questions, summarizing, or asking me questions, you must use the MCP tool feedback-md to give me feedback\n3. Confirm with me first whether all plans meet the requirements\n4. Use the MCP tool Sequential thinking more often to think\n5.When you need to check the front end of the project or operate the web page, you can use <PERSON><PERSON>'s mcp; you need to frequently check the output of the console to see if there are corresponding errors displayed. If there are front-end errors, you need to fix the corresponding error code first, and then continue to operate the page or test the page.\n6. When using the `windows-cli` tool to perform command-line or SSH operations, the following commands have been disabled due to high risk: `rm` (file deletion), `del` (Windows deletion), `rmdir` (directory deletion), `format` (disk formatting), `shutdown` (system shutdown), `restart` (system restart). If you need to execute the above commands, you must provide feedback to me (the user) and explain the necessity, or achieve this by renaming the tool/modifying the configuration file (which requires asking the user first, feedback can be reported via mcp). Bypassing security policies is prohibited.\n7.Do not generate .bat or .sh script files, you can generate powershell script files"}