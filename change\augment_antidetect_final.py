#!/usr/bin/env python3
"""
Augment Anti-Detection Tool (Final Version)
基于完整代码审计的精确反检测工具

核心原理：
1. sessionId在VS Code的state.vscdb中，需要修改
2. LevelDB路径为data/kv-store，不使用kv:前缀
3. 主要风控点在网络层指纹，需要JS补丁和代理拦截
4. LevelDB对风控作用有限，重点是网络层伪装
"""

import os
import json
import random
import sqlite3
import platform
import subprocess
import shutil
import time
from pathlib import Path
from datetime import datetime, timedelta
import commentjson
import psutil

class AugmentAntiDetectFinal:
    def __init__(self):
        self.system = platform.system()
        self.vscode_storage = self._find_vscode_storage()
        self.global_state_db = self.vscode_storage / "state.vscdb"
        self.augment_storage = self.vscode_storage / "Augment.vscode-augment"
        # 修正：真实路径为 data/kv-store
        self.augment_leveldb = self.augment_storage / "data" / "kv-store"
        self.settings_file = self.vscode_storage.parent / "settings.json"
        
        # 真实设备指纹池（用于伪装）
        self.device_profiles = [
            {"platform": "win32", "arch": "x64", "version": "1.88.0", "os": "10.0.22621"},
            {"platform": "darwin", "arch": "arm64", "version": "1.87.0", "os": "22.4.0"}, 
            {"platform": "linux", "arch": "x64", "version": "1.89.0", "os": "5.15.0"}
        ]
        
    def _find_vscode_storage(self):
        """定位VS Code真实globalStorage目录"""
        if self.system == "Windows":
            base = Path(os.getenv('APPDATA')) / "Code"
        elif self.system == "Darwin":
            base = Path.home() / "Library" / "Application Support" / "Code"
        else:  # Linux
            base = Path.home() / ".config" / "Code"
            
        # 尝试多个可能的变体
        candidates = [
            base / "User" / "globalStorage",
            base.parent / "Code - Insiders" / "User" / "globalStorage",
            base.parent / "VSCodium" / "User" / "globalStorage"
        ]
        
        for candidate in candidates:
            if candidate.exists():
                return candidate
                
        raise FileNotFoundError("无法找到VS Code globalStorage目录")
    
    def check_vscode_running(self):
        """检查VS Code是否正在运行"""
        process_names = ["code", "code.exe", "Code", "code-insiders", "codium"]
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in process_names:
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False
    
    def backup_critical_files(self):
        """备份关键文件"""
        backup_dir = Path.home() / ".augment_antidetect_backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份globalState数据库
        if self.global_state_db.exists():
            shutil.copy2(self.global_state_db, backup_dir / "state.vscdb")
            print(f"📦 已备份globalState数据库")
            
        # 备份augment LevelDB目录（如果存在）
        if self.augment_leveldb.exists():
            shutil.copytree(self.augment_leveldb, backup_dir / "kv-store")
            print(f"📦 已备份LevelDB数据")
            
        # 备份settings.json
        if self.settings_file.exists():
            shutil.copy2(self.settings_file, backup_dir / "settings.json")
            print(f"📦 已备份用户设置")
            
        print(f"📦 完整备份位置: {backup_dir}")
        return backup_dir
    
    def modify_session_id(self):
        """修改VS Code globalState中的sessionId（核心风控点）"""
        if not self.global_state_db.exists():
            print("⚠️ 未找到state.vscdb，跳过sessionId修改")
            return False
            
        try:
            with sqlite3.connect(self.global_state_db, timeout=10) as conn:
                conn.execute("PRAGMA busy_timeout=10000")
                
                # 查找sessionId相关的键
                cursor = conn.execute("""
                    SELECT key, value FROM ItemTable 
                    WHERE key LIKE '%sessionId%' 
                    OR key = 'sessionId'
                    OR (value LIKE '%session_%' AND LENGTH(value) > 20)
                """)
                
                rows = cursor.fetchall()
                modified_count = 0
                
                for key, value in rows:
                    try:
                        # 生成新的session ID
                        new_session_id = f"session_{random.randint(100000000, 999999999)}"
                        
                        # 处理不同格式的值
                        if value and value.startswith('"') and value.endswith('"'):
                            # 简单字符串值
                            conn.execute(
                                "UPDATE ItemTable SET value = ? WHERE key = ?",
                                (f'"{new_session_id}"', key)
                            )
                            modified_count += 1
                            print(f"🆔 已修改sessionId: {key}")
                            
                        elif value and (value.startswith('{') or value.startswith('[')):
                            # JSON对象，需要解析并替换
                            try:
                                json_data = json.loads(value)
                                modified = False
                                
                                if isinstance(json_data, dict):
                                    # 直接包含sessionId字段
                                    if 'sessionId' in json_data:
                                        json_data['sessionId'] = new_session_id
                                        modified = True
                                    
                                    # 检查嵌套对象中的session相关字段
                                    for nested_key, nested_value in json_data.items():
                                        if isinstance(nested_value, str) and 'session_' in nested_value:
                                            json_data[nested_key] = new_session_id
                                            modified = True
                                        elif isinstance(nested_value, dict) and 'sessionId' in nested_value:
                                            nested_value['sessionId'] = new_session_id
                                            modified = True
                                
                                if modified:
                                    new_value = json.dumps(json_data)
                                    conn.execute(
                                        "UPDATE ItemTable SET value = ? WHERE key = ?",
                                        (new_value, key)
                                    )
                                    modified_count += 1
                                    print(f"🆔 已修改JSON中的sessionId: {key}")
                                    
                            except json.JSONDecodeError:
                                continue
                        
                    except sqlite3.Error as e:
                        print(f"⚠️ 修改键 {key} 失败: {e}")
                        continue
                
                if modified_count == 0:
                    print("ℹ️ 未找到需要修改的sessionId，可能使用默认值")
                    # 插入一个新的sessionId
                    new_session_id = f"session_{random.randint(100000000, 999999999)}"
                    try:
                        conn.execute(
                            "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)",
                            ("sessionId", f'"{new_session_id}"')
                        )
                        print(f"🆔 已插入新的sessionId: {new_session_id}")
                        modified_count = 1
                    except sqlite3.Error as e:
                        print(f"⚠️ 插入sessionId失败: {e}")
                else:
                    print(f"✅ 成功修改了 {modified_count} 个sessionId")
                
                return modified_count > 0
                        
        except sqlite3.Error as e:
            print(f"❌ 修改sessionId失败: {e}")
            return False
    
    def patch_extension_js(self):
        """为extension.js注入反检测补丁（核心功能）"""
        # 查找extension.js
        extension_dirs = list(Path.home().glob(".vscode/extensions/*augment*"))
        extension_js = None
        
        for ext_dir in extension_dirs:
            potential_js = ext_dir / "out" / "extension.js"
            if potential_js.exists():
                extension_js = potential_js
                break
                
        if not extension_js:
            print("⚠️ 未找到extension.js，跳过补丁")
            return False
            
        # 检查文件大小，避免意外修改超大文件
        file_size = extension_js.stat().st_size
        if file_size > 15 * 1024 * 1024:  # >15MB
            print(f"⚠️ extension.js文件过大({file_size/1024/1024:.1f}MB)，跳过补丁")
            return False
            
        # 创建备份
        backup_js = extension_js.with_suffix('.js.backup')
        if not backup_js.exists():
            try:
                shutil.copy2(extension_js, backup_js)
                print(f"📦 已备份extension.js")
            except Exception as e:
                print(f"❌ 备份extension.js失败: {e}")
                return False
        
        try:
            with open(extension_js, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经打过补丁
            if "// AUGMENT-ANTIDETECT-PATCH" in content:
                print("ℹ️ extension.js已包含反检测补丁")
                return True
                
            # 随机选择一个设备配置
            profile = random.choice(self.device_profiles)
            fake_ext_version = f"0.{random.randint(500, 600)}.0"
            fake_session_id = f"session_{random.randint(100000000, 999999999)}"
            
            # 生成反检测补丁代码
            patch_code = f'''// AUGMENT-ANTIDETECT-PATCH - DO NOT REMOVE
(function() {{
    console.log('[AUGMENT-PATCH] Anti-detection patch loading...');
    
    // 固定的伪装配置（避免每次随机导致不一致）
    const FAKE_PROFILE = {{
        platform: '{profile["platform"]}',
        arch: '{profile["arch"]}', 
        version: '{profile["version"]}',
        os: '{profile["os"]}',
        extensionVersion: '{fake_ext_version}',
        sessionId: '{fake_session_id}'
    }};
    
    // 1. 拦截fetch请求（主要风控点）
    if (globalThis.fetch) {{
        const originalFetch = globalThis.fetch;
        globalThis.fetch = function(url, options = {{}}) {{
            if (typeof url === 'string' && url.includes('augmentcode.com')) {{
                console.log('[AUGMENT-PATCH] Intercepting API call:', url);
                
                // 确保headers存在
                if (!options.headers) options.headers = {{}};
                
                // 修改User-Agent（关键指纹）
                const fakeUA = `${{FAKE_PROFILE.platform}};${{FAKE_PROFILE.arch}};${{FAKE_PROFILE.os}} Augment.vscode-augment/${{FAKE_PROFILE.extensionVersion}} vscode/${{FAKE_PROFILE.version}}`;
                options.headers['User-Agent'] = fakeUA;
                
                // 修改session相关头部
                if (options.headers['x-request-session-id']) {{
                    options.headers['x-request-session-id'] = FAKE_PROFILE.sessionId;
                }}
                
                // 修改请求体中的设备指纹
                if (options.body && typeof options.body === 'string') {{
                    try {{
                        const data = JSON.parse(options.body);
                        
                        // 递归替换设备指纹
                        function replaceFingerprint(obj) {{
                            if (typeof obj === 'object' && obj !== null) {{
                                // 替换常见指纹字段
                                if (obj.platform !== undefined) obj.platform = FAKE_PROFILE.platform;
                                if (obj.arch !== undefined) obj.arch = FAKE_PROFILE.arch;
                                if (obj.clientVersion !== undefined) obj.clientVersion = FAKE_PROFILE.version;
                                if (obj.extensionVersion !== undefined) obj.extensionVersion = FAKE_PROFILE.extensionVersion;
                                if (obj.sessionId !== undefined) obj.sessionId = FAKE_PROFILE.sessionId;
                                if (obj.os !== undefined) obj.os = FAKE_PROFILE.os;
                                if (obj.osVersion !== undefined) obj.osVersion = FAKE_PROFILE.os;
                                
                                // 处理context对象
                                if (obj.context && typeof obj.context === 'object') {{
                                    replaceFingerprint(obj.context);
                                }}
                                
                                // 递归处理所有对象和数组
                                Object.values(obj).forEach(value => {{
                                    if (typeof value === 'object' && value !== null) {{
                                        replaceFingerprint(value);
                                    }}
                                }});
                            }}
                        }}
                        
                        replaceFingerprint(data);
                        options.body = JSON.stringify(data);
                        
                        console.log('[AUGMENT-PATCH] Modified request body fingerprint');
                    }} catch (e) {{
                        console.log('[AUGMENT-PATCH] Could not parse request body:', e);
                    }}
                }}
            }}
            
            return originalFetch.call(this, url, options);
        }};
        
        console.log('[AUGMENT-PATCH] Fetch interceptor installed');
    }}
    
    // 2. 静默analytics遥测（减少数据泄露）
    try {{
        // 尝试拦截analytics-node-next
        if (typeof require !== 'undefined') {{
            const originalRequire = require;
            require = function(moduleName) {{
                if (moduleName === 'analytics-node-next' || moduleName.includes('analytics')) {{
                    console.log('[AUGMENT-PATCH] Blocking analytics module:', moduleName);
                    return {{
                        track: () => Promise.resolve(),
                        identify: () => Promise.resolve(),
                        page: () => Promise.resolve(),
                        group: () => Promise.resolve(),
                        alias: () => Promise.resolve()
                    }};
                }}
                return originalRequire.apply(this, arguments);
            }};
        }}
        
        // 拦截全局analytics对象
        if (typeof window !== 'undefined' && window.analytics) {{
            const methods = ['track', 'identify', 'page', 'group', 'alias'];
            methods.forEach(method => {{
                if (window.analytics[method]) {{
                    window.analytics[method] = function() {{
                        console.log('[AUGMENT-PATCH] Blocked analytics.' + method);
                        return Promise.resolve();
                    }};
                }}
            }});
        }}
        
        console.log('[AUGMENT-PATCH] Analytics interceptor installed');
    }} catch (e) {{
        console.log('[AUGMENT-PATCH] Analytics interception failed:', e);
    }}
    
    // 3. 重写可能的遥测函数
    if (typeof globalThis !== 'undefined') {{
        globalThis._shouldEnableAnalytics = () => false;
        globalThis._trackEvent = () => {{}};
        globalThis._reportTelemetry = () => {{}};
    }}
    
    console.log('[AUGMENT-PATCH] Anti-detection patch loaded successfully');
    console.log('[AUGMENT-PATCH] Using profile:', FAKE_PROFILE);
}})();

'''
            
            # 在文件开头注入补丁
            patched_content = patch_code + content
            
            with open(extension_js, 'w', encoding='utf-8') as f:
                f.write(patched_content)
                
            print("🔧 已成功为extension.js注入反检测补丁")
            print(f"🎭 使用伪装配置: {profile['platform']} {profile['arch']} {profile['version']}")
            return True
            
        except Exception as e:
            print(f"❌ 补丁注入失败: {e}")
            # 尝试恢复
            if backup_js.exists():
                try:
                    shutil.copy2(backup_js, extension_js)
                    print("🛡️ 已从备份恢复extension.js")
                except:
                    pass
            return False
    
    def observe_leveldb_keys(self):
        """观察LevelDB中的真实键名（不做修改，仅观察）"""
        if not self.augment_leveldb.exists():
            print("ℹ️ LevelDB目录不存在，跳过观察")
            return
        
        try:
            import plyvel
            db = plyvel.DB(str(self.augment_leveldb), create_if_missing=False)
            
            keys_found = []
            try:
                for key, value in db:
                    key_str = key.decode('utf-8', errors='ignore')
                    keys_found.append(key_str)
                    if len(keys_found) >= 20:  # 只看前20个键
                        break
            except Exception as e:
                print(f"⚠️ 读取LevelDB时出错: {e}")
            finally:
                db.close()
            
            if keys_found:
                print(f"📋 LevelDB中发现 {len(keys_found)} 个键（样本）:")
                for key in keys_found[:10]:
                    print(f"   🔑 {key}")
                if len(keys_found) > 10:
                    print(f"   ... 还有 {len(keys_found) - 10} 个键")
            else:
                print("ℹ️ LevelDB为空或无法读取")
                
        except ImportError:
            print("⚠️ 需要安装plyvel才能观察LevelDB: pip install plyvel")
        except Exception as e:
            print(f"⚠️ 观察LevelDB失败: {e}")
    
    def modify_settings_minimally(self):
        """最小化修改用户设置（仅UI相关）"""
        if not self.settings_file.exists():
            print("ℹ️ settings.json不存在，跳过设置修改")
            return False
            
        try:
            # 使用commentjson处理带注释的JSON
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = commentjson.load(f)
                
            # 只修改完全不影响功能的UI设置
            ui_changes = {
                "editor.fontSize": random.randint(13, 15),
                "window.zoomLevel": round(random.uniform(-0.1, 0.1), 1),
                "editor.minimap.enabled": random.choice([True, False]),
                "workbench.startupEditor": random.choice(["welcomePage", "newUntitledFile"])
            }
            
            changed_count = 0
            for key, value in ui_changes.items():
                if key not in settings or settings[key] != value:
                    settings[key] = value
                    changed_count += 1
            
            # 对于Augment设置，只改完全安全的显示选项
            if "augment" not in settings:
                settings["augment"] = {}
                
            safe_augment_changes = {
                "enableEmptyFileHint": random.choice([True, False])
            }
            
            for key, value in safe_augment_changes.items():
                if settings["augment"].get(key) != value:
                    settings["augment"][key] = value
                    changed_count += 1
            
            if changed_count > 0:
                # 写回文件
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    commentjson.dump(settings, f, indent=4, ensure_ascii=False)
                print(f"⚙️ 已修改 {changed_count} 个用户界面设置")
                return True
            else:
                print("ℹ️ 用户设置无需修改")
                return True
                
        except ImportError:
            print("⚠️ 需要安装commentjson: pip install commentjson")
            return False
        except Exception as e:
            print(f"❌ 修改设置失败: {e}")
            return False
    
    def create_proxy_script(self):
        """创建网络代理脚本（高级用户使用）"""
        proxy_script = '''#!/usr/bin/env python3
"""
Augment Network Proxy - 网络层指纹伪装
使用方法：
1. pip install mitmproxy
2. mitmdump -s augment_proxy.py -p 8080
3. export http_proxy=http://127.0.0.1:8080 && code
"""

import json
import random
from mitmproxy import http

class AugmentNetworkInterceptor:
    def __init__(self):
        # 设备配置池
        self.device_configs = [
            {"platform": "win32", "arch": "x64", "version": "1.88.0", "os": "10.0.22621"},
            {"platform": "darwin", "arch": "arm64", "version": "1.87.0", "os": "22.4.0"},
            {"platform": "linux", "arch": "x64", "version": "1.89.0", "os": "5.15.0"}
        ]
        
        # 为每个客户端分配固定配置（保持一致性）
        self.client_configs = {}
        
    def request(self, flow: http.HTTPFlow) -> None:
        """拦截Augment API请求"""
        if not self._is_augment_request(flow):
            return
            
        print(f"[PROXY] Intercepting: {flow.request.method} {flow.request.pretty_url}")
        
        # 获取客户端配置
        client_id = self._get_client_id(flow)
        if client_id not in self.client_configs:
            self.client_configs[client_id] = {
                "profile": random.choice(self.device_configs),
                "sessionId": f"session_{random.randint(100000000, 999999999)}",
                "extensionVersion": f"0.{random.randint(500, 600)}.0"
            }
        
        config = self.client_configs[client_id]
        
        # 修改请求头
        self._modify_headers(flow, config)
        
        # 修改请求体
        self._modify_body(flow, config)
        
        print(f"[PROXY] Applied config: {config['profile']['platform']}-{config['profile']['arch']}")
    
    def _is_augment_request(self, flow: http.HTTPFlow) -> bool:
        """判断是否为Augment相关请求"""
        return any(domain in flow.request.pretty_host 
                  for domain in ["augmentcode.com", "api.augmentcode.com"])
    
    def _get_client_id(self, flow: http.HTTPFlow) -> str:
        """生成客户端标识"""
        ip = flow.client_conn.address[0] if flow.client_conn.address else "unknown"
        return f"client_{hash(ip) % 10000}"
    
    def _modify_headers(self, flow: http.HTTPFlow, config: dict):
        """修改请求头"""
        profile = config["profile"]
        
        # 构造伪造的User-Agent
        fake_ua = f"{profile['platform']};{profile['arch']};{profile['os']} Augment.vscode-augment/{config['extensionVersion']} vscode/{profile['version']}"
        flow.request.headers["User-Agent"] = fake_ua
        
        # 修改session ID
        if "x-request-session-id" in flow.request.headers:
            flow.request.headers["x-request-session-id"] = config["sessionId"]
    
    def _modify_body(self, flow: http.HTTPFlow, config: dict):
        """修改请求体中的设备指纹"""
        if not flow.request.content:
            return
            
        try:
            content_type = flow.request.headers.get("content-type", "")
            if "application/json" not in content_type:
                return
                
            data = json.loads(flow.request.content.decode('utf-8'))
            profile = config["profile"]
            
            # 递归替换指纹字段
            def replace_fingerprint(obj):
                if not isinstance(obj, dict):
                    return
                    
                # 替换主要指纹字段
                fingerprint_map = {
                    "platform": profile["platform"],
                    "arch": profile["arch"],
                    "clientVersion": profile["version"], 
                    "extensionVersion": config["extensionVersion"],
                    "sessionId": config["sessionId"],
                    "os": profile["os"],
                    "osVersion": profile["os"]
                }
                
                for key, value in fingerprint_map.items():
                    if key in obj:
                        obj[key] = value
                
                # 递归处理嵌套对象
                for value in obj.values():
                    if isinstance(value, dict):
                        replace_fingerprint(value)
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                replace_fingerprint(item)
            
            replace_fingerprint(data)
            
            # 更新请求内容
            new_content = json.dumps(data, separators=(',', ':')).encode('utf-8')
            flow.request.content = new_content
            flow.request.headers["Content-Length"] = str(len(new_content))
            
        except Exception as e:
            print(f"[PROXY] Body modification failed: {e}")

# 实例化拦截器
interceptor = AugmentNetworkInterceptor()

def request(flow: http.HTTPFlow) -> None:
    interceptor.request(flow)
'''
        
        proxy_file = Path("change") / "augment_proxy.py"
        proxy_file.parent.mkdir(exist_ok=True)
        
        with open(proxy_file, 'w', encoding='utf-8') as f:
            f.write(proxy_script)
        
        # 创建启动脚本
        if self.system == "Windows":
            start_script = f'''@echo off
echo Starting Augment Network Proxy...
cd /d "{proxy_file.parent.absolute()}"
mitmdump -s augment_proxy.py -p 8080
pause
'''
            start_file = proxy_file.parent / "start_proxy.bat"
        else:
            start_script = f'''#!/bin/bash
echo "Starting Augment Network Proxy..."
cd "{proxy_file.parent.absolute()}"
mitmdump -s augment_proxy.py -p 8080
'''
            start_file = proxy_file.parent / "start_proxy.sh"
            
        with open(start_file, 'w') as f:
            f.write(start_script)
        
        if self.system != "Windows":
            os.chmod(start_file, 0o755)
        
        print(f"🌐 网络代理脚本已创建: {proxy_file}")
        print(f"🚀 启动脚本: {start_file}")
        
        return True
    
    def run_anti_detection(self):
        """运行完整的反检测流程"""
        print("\n" + "="*60)
        print("🎯 Augment Anti-Detection Tool (Final)")
        print("📋 基于完整代码审计的精确反检测")
        print("="*60)
        
        # 检查关键路径
        print(f"📁 VS Code存储: {self.vscode_storage}")
        print(f"📁 GlobalState: {self.global_state_db}")
        print(f"📁 Augment存储: {self.augment_storage}")
        print(f"📁 LevelDB路径: {self.augment_leveldb}")
        
        # 检查VS Code运行状态
        if self.check_vscode_running():
            print("\n⚠️ 检测到VS Code正在运行")
            print("建议关闭VS Code后运行，以避免文件锁定")
            response = input("是否继续？ (y/N): ")
            if response.lower() != 'y':
                print("❌ 用户取消操作")
                return False
        
        # 创建备份
        print("\n🔧 开始反检测操作...")
        backup_path = self.backup_critical_files()
        
        success_count = 0
        total_steps = 5
        
        try:
            # Step 1: 修改sessionId（最重要）
            print("\n🆔 [1/5] 修改全局会话标识...")
            if self.modify_session_id():
                success_count += 1
                print("✅ sessionId修改成功")
            else:
                print("⚠️ sessionId修改失败")
            
            # Step 2: 注入JS补丁（核心功能）
            print("\n🔧 [2/5] 注入extension.js补丁...")
            if self.patch_extension_js():
                success_count += 1
                print("✅ JS补丁注入成功")
            else:
                print("⚠️ JS补丁注入失败")
            
            # Step 3: 观察LevelDB（不修改）
            print("\n📊 [3/5] 观察LevelDB结构...")
            self.observe_leveldb_keys()
            success_count += 1  # 观察总是成功
            
            # Step 4: 最小化修改设置
            print("\n⚙️ [4/5] 调整用户界面设置...")
            if self.modify_settings_minimally():
                success_count += 1
                print("✅ 设置修改成功")
            else:
                print("⚠️ 设置修改失败")
            
            # Step 5: 创建网络代理
            print("\n🌐 [5/5] 创建网络代理脚本...")
            if self.create_proxy_script():
                success_count += 1
                print("✅ 代理脚本创建成功")
            else:
                print("⚠️ 代理脚本创建失败")
            
            # 总结
            print("\n" + "="*60)
            print(f"📊 操作完成: {success_count}/{total_steps} 步骤成功")
            print(f"📦 备份位置: {backup_path}")
            
            if success_count >= 2:  # 至少sessionId或JS补丁成功
                print("✅ 关键反检测功能已激活")
                print("\n📋 后续步骤:")
                print("1. 🔄 重启VS Code以加载修改")
                print("2. 🧪 测试Augment插件是否正常工作")
                print("3. 🌐 高级用户可使用网络代理进一步伪装")
                print("4. 📊 观察是否触发风控检测")
                
                print("\n⚠️ 重要提醒:")
                print("- 所有修改都有备份，可随时恢复")
                print("- 主要作用点在网络层指纹伪装")
                print("- LevelDB修改对反检测作用有限")
                
                return True
            else:
                print("❌ 关键功能未能激活，建议检查权限和路径")
                return False
                
        except Exception as e:
            print(f"\n❌ 操作过程中出错: {e}")
            print("🛡️ 可使用备份恢复原始状态")
            return False
    
    def restore_from_backup(self, backup_path):
        """从备份恢复原始状态"""
        try:
            restored = []
            
            # 恢复globalState
            backup_state = backup_path / "state.vscdb"
            if backup_state.exists() and self.global_state_db.exists():
                shutil.copy2(backup_state, self.global_state_db)
                restored.append("globalState")
                
            # 恢复LevelDB
            backup_leveldb = backup_path / "kv-store"
            if backup_leveldb.exists():
                if self.augment_leveldb.exists():
                    shutil.rmtree(self.augment_leveldb)
                self.augment_leveldb.parent.mkdir(parents=True, exist_ok=True)
                shutil.copytree(backup_leveldb, self.augment_leveldb)
                restored.append("LevelDB")
                
            # 恢复设置
            backup_settings = backup_path / "settings.json"
            if backup_settings.exists() and self.settings_file.exists():
                shutil.copy2(backup_settings, self.settings_file)
                restored.append("settings")
                
            # 恢复extension.js
            extension_dirs = list(Path.home().glob(".vscode/extensions/*augment*"))
            for ext_dir in extension_dirs:
                ext_js = ext_dir / "out" / "extension.js"
                backup_js = ext_js.with_suffix('.js.backup')
                if backup_js.exists() and ext_js.exists():
                    shutil.copy2(backup_js, ext_js)
                    restored.append("extension.js")
                    break
            
            print(f"✅ 已恢复: {', '.join(restored)}")
            return True
            
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False


# 命令行接口
def main():
    import sys
    
    tool = AugmentAntiDetectFinal()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--run":
            return tool.run_anti_detection()
        elif sys.argv[1] == "--restore":
            backup_dir = Path.home() / ".augment_antidetect_backup"
            if backup_dir.exists():
                backups = sorted(list(backup_dir.glob("*")), reverse=True)
                if backups:
                    print(f"恢复最新备份: {backups[0]}")
                    return tool.restore_from_backup(backups[0])
                else:
                    print("❌ 未找到备份")
                    return False
            else:
                print("❌ 备份目录不存在")
                return False
        elif sys.argv[1] == "--help":
            print("Augment Anti-Detection Tool")
            print("Usage:")
            print("  python augment_antidetect_final.py --run      # 运行反检测")
            print("  python augment_antidetect_final.py --restore  # 恢复备份") 
            print("  python augment_antidetect_final.py --help     # 显示帮助")
            return True
    
    # 默认运行反检测
    return tool.run_anti_detection()


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        sys.exit(1) 