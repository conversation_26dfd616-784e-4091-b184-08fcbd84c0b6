import{S as x,i as m,s as v,W as b,a as y,d as z,D as g,t as d,q as f,_ as u,g as h,$ as p,c as j,E as k,F as q,G as w,Z as B,J as D,K as E,L as F,M as G}from"./SpinnerAugment-VfHtkDdv.js";import"./IconButtonAugment-BlRCK7lJ.js";function J(n){let s;const c=n[5].default,t=D(c,n,n[6],null);return{c(){t&&t.c()},m(a,i){t&&t.m(a,i),s=!0},p(a,i){t&&t.p&&(!s||64&i)&&E(t,c,a,a[6],s?G(c,a[6],i,null):F(a[6]),null)},i(a){s||(f(t,a),s=!0)},o(a){d(t,a),s=!1},d(a){t&&t.d(a)}}}function K(n){let s,c,t,a;c=new b({props:{type:n[2],size:n[1],$$slots:{default:[J]},$$scope:{ctx:n}}});let i=[n[4],{class:t=`c-base-text-input c-base-text-input--${n[0]} c-base-text-input--size-${n[1]}`}],l={};for(let e=0;e<i.length;e+=1)l=y(l,i[e]);return{c(){s=q("div"),w(c.$$.fragment),u(s,l),p(s,"c-base-text-input--has-color",n[3]!==void 0),p(s,"svelte-1jrck44",!0)},m(e,o){j(e,s,o),k(c,s,null),a=!0},p(e,[o]){const $={};4&o&&($.type=e[2]),2&o&&($.size=e[1]),64&o&&($.$$scope={dirty:o,ctx:e}),c.$set($),u(s,l=h(i,[16&o&&e[4],(!a||3&o&&t!==(t=`c-base-text-input c-base-text-input--${e[0]} c-base-text-input--size-${e[1]}`))&&{class:t}])),p(s,"c-base-text-input--has-color",e[3]!==void 0),p(s,"svelte-1jrck44",!0)},i(e){a||(f(c.$$.fragment,e),a=!0)},o(e){d(c.$$.fragment,e),a=!1},d(e){e&&z(s),g(c)}}}function L(n,s,c){let t,{$$slots:a={},$$scope:i}=s,{variant:l="surface"}=s,{size:e=2}=s,{type:o="default"}=s,{color:$}=s;return n.$$set=r=>{"variant"in r&&c(0,l=r.variant),"size"in r&&c(1,e=r.size),"type"in r&&c(2,o=r.type),"color"in r&&c(3,$=r.color),"$$scope"in r&&c(6,i=r.$$scope)},n.$$.update=()=>{8&n.$$.dirty&&c(4,t=B($||"accent"))},[l,e,o,$,t,a,i]}class W extends x{constructor(s){super(),m(this,s,L,K,v,{variant:0,size:1,type:2,color:3})}}export{W as B};
