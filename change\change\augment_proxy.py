
import json
import random
import uuid
from mitmproxy import http

class AugmentAntiDetectionProxy:
    def __init__(self):
        self.device_profiles = {
            "platforms": [
                {"name": "win32", "archs": ["x64", "arm64"]},
                {"name": "darwin", "archs": ["x64", "arm64"]},
                {"name": "linux", "archs": ["x64", "arm64"]}
            ],
            "vscode_versions": ["1.85.0", "1.86.0", "1.87.0", "1.88.0", "1.89.0"]
        }
    
    def request(self, flow: http.HTTPFlow) -> None:
        # 只拦截Augment相关请求
        if "augment" in flow.request.pretty_host.lower():
            self.modify_request(flow)
    
    def modify_request(self, flow: http.HTTPFlow):
        print(f"[Anti-Detection Proxy] 拦截请求: {flow.request.url}")
        
        # 选择随机设备配置
        profile = random.choice(self.device_profiles["platforms"])
        fake_arch = random.choice(profile["archs"])
        fake_vscode = random.choice(self.device_profiles["vscode_versions"])
        
        # 修改请求头
        headers = flow.request.headers
        
        # 修改User-Agent
        if "user-agent" in headers:
            fake_ua = f'{profile["name"]};{fake_arch};6.1.0 Augment.vscode-augment/0.516.0 vscode/{fake_vscode}'
            headers["user-agent"] = fake_ua
            print(f"  ✓ 修改User-Agent: {fake_ua}")
        
        # 修改会话ID
        if "x-request-session-id" in headers:
            new_session = str(uuid.uuid4())
            headers["x-request-session-id"] = new_session
            print(f"  ✓ 修改session-id: {new_session}")
        
        # 修改请求体中的设备信息
        if flow.request.content:
            try:
                data = json.loads(flow.request.content.decode())
                if isinstance(data, dict):
                    # 修改常见的设备指纹字段
                    if "platform" in data:
                        data["platform"] = profile["name"]
                    if "arch" in data:
                        data["arch"] = fake_arch
                    if "sessionId" in data:
                        data["sessionId"] = str(uuid.uuid4())
                    
                    flow.request.content = json.dumps(data).encode()
                    print("  ✓ 修改请求体设备信息")
            except:
                pass  # 不是JSON格式，跳过

addons = [AugmentAntiDetectionProxy()]
