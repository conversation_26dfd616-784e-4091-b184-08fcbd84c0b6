#!/usr/bin/env python3
"""
Augment Anti-Detection Quick Start (修复版)
处理Windows下的依赖安装问题
"""

import sys
import subprocess
import platform
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    system = platform.system()
    
    # 必要依赖
    required_packages = ["psutil", "commentjson"]
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing.append(package)
            print(f"❌ {package} 未安装")
    
    # 可选依赖
    optional_packages = {"plyvel": "LevelDB操作（可选，Windows下可能编译失败）"}
    optional_missing = []
    
    for package, desc in optional_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} 已安装 - {desc}")
        except ImportError:
            optional_missing.append(f"{package} - {desc}")
            print(f"⚠️ {package} 未安装 - {desc}")
    
    if missing:
        print(f"\n❌ 缺少必要依赖:")
        for pkg in missing:
            print(f"   - {pkg}")
        print(f"\n📥 安装命令: pip install {' '.join(missing)}")
        
        # 询问是否自动安装
        if input("\n是否自动安装必要依赖? (y/n): ").lower() == 'y':
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing)
                print("✅ 必要依赖安装完成")
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败，请手动安装")
                return False
    
    if optional_missing:
        print(f"\n⚠️ 可选依赖未安装:")
        for pkg in optional_missing:
            print(f"   - {pkg}")
        
        if system == "Windows":
            print(f"\n💡 Windows用户注意:")
            print(f"   - plyvel在Windows下可能编译失败，这是正常的")
            print(f"   - 脚本已包含fallback机制，不影响使用")
            print(f"   - 如需强制安装plyvel，请先安装Visual Studio Build Tools")
    
    return True

def install_optional_dependencies():
    """尝试安装可选依赖"""
    system = platform.system()
    
    if system == "Windows":
        print("🔧 Windows环境下安装可选依赖...")
        print("⚠️ 注意: plyvel在Windows下可能需要编译，可能失败")
        
        if input("是否尝试安装plyvel? (y/n): ").lower() == 'y':
            try:
                print("📦 尝试安装预编译版本...")
                # 尝试从conda-forge安装预编译版本
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "plyvel", "--only-binary=all"])
                    print("✅ plyvel预编译版本安装成功")
                except subprocess.CalledProcessError:
                    print("⚠️ 预编译版本安装失败，尝试编译安装...")
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "plyvel"])
                    print("✅ plyvel编译安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ plyvel安装失败: {e}")
                print("💡 这是正常的，脚本会使用fallback机制")
    else:
        # Linux/macOS通常可以正常编译
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "plyvel"])
            print("✅ plyvel安装成功")
        except subprocess.CalledProcessError:
            print("❌ plyvel安装失败，将使用fallback机制")

def run_main_script():
    """运行主脚本"""
    main_script = Path(__file__).parent / "augment_antidetect_corrected.py"
    
    if not main_script.exists():
        print(f"❌ 未找到主脚本: {main_script}")
        return False
    
    print(f"\n🚀 启动主脚本: {main_script}")
    
    try:
        subprocess.check_call([sys.executable, str(main_script), "--run"])
        return True
    except subprocess.CalledProcessError:
        print("❌ 主脚本执行失败")
        return False

def show_troubleshooting():
    """显示故障排除信息"""
    system = platform.system()
    
    print("\n🔧 故障排除指南:")
    print("="*50)
    
    if system == "Windows":
        print("Windows环境:")
        print("1. plyvel编译失败是正常的，脚本有fallback机制")
        print("2. 如需强制安装plyvel，安装Visual Studio Build Tools:")
        print("   https://visualstudio.microsoft.com/visual-cpp-build-tools/")
        print("3. 或者使用conda安装:")
        print("   conda install -c conda-forge plyvel")
    
    print("\n通用问题:")
    print("1. 权限问题: 以管理员/sudo权限运行")
    print("2. 网络问题: 配置pip镜像源")
    print("3. Python版本: 建议使用Python 3.7+")
    
    print("\n文件位置:")
    print(f"- 脚本目录: {Path(__file__).parent}")
    print(f"- 备份目录: {Path(__file__).parent / 'backup_*'}")
    
def main():
    print("🔧 Augment Anti-Detection Tool - 快速启动 (修复版)")
    print("="*60)
    
    # 显示系统信息
    system = platform.system()
    python_version = sys.version.split()[0]
    print(f"🖥️ 系统: {system}")
    print(f"🐍 Python: {python_version}")
    
    # 检查依赖
    print(f"\n📦 检查依赖...")
    if not check_dependencies():
        show_troubleshooting()
        return
    
    # 主菜单
    while True:
        print(f"\n📋 请选择操作:")
        print("1. 运行反检测脚本")
        print("2. 尝试安装可选依赖 (plyvel)")
        print("3. 显示故障排除信息")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            if run_main_script():
                print("\n✅ 脚本执行完成")
                break
            else:
                print("\n❌ 脚本执行失败")
                
        elif choice == "2":
            install_optional_dependencies()
            
        elif choice == "3":
            show_troubleshooting()
            
        elif choice == "4":
            print("👋 退出")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main() 