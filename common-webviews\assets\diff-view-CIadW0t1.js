var rn=Object.defineProperty;var sn=(o,t,e)=>t in o?rn(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var _=(o,t,e)=>sn(o,typeof t!="symbol"?t+"":t,e);import{B as $e,C as an,A as Lt,aq as ci,ap as ct,ar as Ae,S as Yt,i as Jt,s as te,aa as $t,d as x,D as S,P as De,t as b,q as y,$ as nt,o as ut,p as dt,Q as Tt,c as O,e as U,E as N,ag as Ei,R as kt,F as H,V as Z,G as A,h as q,al as Wt,Y as ot,n as G,a3 as Qt,a4 as cn,a5 as un,an as xi,X as je,N as ye,a6 as _t,a as Le,b as ui,g as dn,u as ln,v as fn,w as hn,x as pn,f as gn,H as mn,j as di,ah as Oi,af as Ii,am as _n,a7 as $n,ad as yn}from"./SpinnerAugment-VfHtkDdv.js";import"./design-system-init-BQpWKoxZ.js";import{h as Xt,W as Y,D as Se,I as vn,g as bn,e as li}from"./IconButtonAugment-BlRCK7lJ.js";import{A as Cn}from"./async-messaging-Cm1y2LK7.js";import{d as fi,a as kn,T as wn}from"./CardAugment-CMpdst0l.js";import{C as Si,S as Ni,K as Vt,a as Mn}from"./folder-opened-CgcyGshw.js";import{r as En,a as hi}from"./monaco-render-utils-DfwV7QLY.js";import{F as xn}from"./index-C5qylk65.js";import{M as Ai}from"./message-broker-DxXjuHCW.js";import{B as wt}from"./ButtonAugment-CRJIYorH.js";import{R as ji,K as On,A as In,P as Sn}from"./Keybindings-BPRMfRV7.js";import{s as Nn}from"./chat-context-DhGlDJgc.js";import{a as An,M as jn,g as Ln,C as Vn}from"./index-BsnNYDaF.js";import"./index-6WVCg-U8.js";import"./file-paths-CXmnYUii.js";import"./types-CGlLNakm.js";import"./BaseTextInput-C9A3t790.js";import"./CalloutAugment-jvmj3vIU.js";import"./exclamation-triangle-BgK0UWCq.js";import"./Filespan-UFj4_Gis.js";import"./MaterialIcon-YT2PSBkc.js";import"./pen-to-square-DiN4Ry3-.js";import"./augment-logo-DHqqkJ4i.js";var Ve={exports:{}};(function(o,t){var e="__lodash_hash_undefined__",n=1,i=2,r=9007199254740991,s="[object Arguments]",a="[object Array]",u="[object AsyncFunction]",p="[object Boolean]",l="[object Date]",f="[object Error]",h="[object Function]",m="[object GeneratorFunction]",C="[object Map]",I="[object Number]",M="[object Null]",$="[object Object]",k="[object Promise]",B="[object Proxy]",X="[object RegExp]",z="[object Set]",et="[object String]",K="[object Symbol]",J="[object Undefined]",D="[object WeakMap]",rt="[object ArrayBuffer]",Et="[object DataView]",ee=/^\[object .+?Constructor\]$/,st=/^(?:0|[1-9]\d*)$/,j={};j["[object Float32Array]"]=j["[object Float64Array]"]=j["[object Int8Array]"]=j["[object Int16Array]"]=j["[object Int32Array]"]=j["[object Uint8Array]"]=j["[object Uint8ClampedArray]"]=j["[object Uint16Array]"]=j["[object Uint32Array]"]=!0,j[s]=j[a]=j[rt]=j[p]=j[Et]=j[l]=j[f]=j[h]=j[C]=j[I]=j[$]=j[X]=j[z]=j[et]=j[D]=!1;var Ft=typeof $e=="object"&&$e&&$e.Object===Object&&$e,xt=typeof self=="object"&&self&&self.Object===Object&&self,tt=Ft||xt||Function("return this")(),w=t&&!t.nodeType&&t,Ot=w&&o&&!o.nodeType&&o,Dt=Ot&&Ot.exports===w,Ut=Dt&&Ft.process,ie=function(){try{return Ut&&Ut.binding&&Ut.binding("util")}catch{}}(),Re=ie&&ie.isTypedArray;function Vi(c,d){for(var g=-1,v=c==null?0:c.length;++g<v;)if(d(c[g],g,c))return!0;return!1}function Fi(c){var d=-1,g=Array(c.size);return c.forEach(function(v,F){g[++d]=[F,v]}),g}function Di(c){var d=-1,g=Array(c.size);return c.forEach(function(v){g[++d]=v}),g}var Pe,ze,Te,Ri=Array.prototype,Pi=Function.prototype,ne=Object.prototype,ve=tt["__core-js_shared__"],We=Pi.toString,lt=ne.hasOwnProperty,Ue=(Pe=/[^.]+$/.exec(ve&&ve.keys&&ve.keys.IE_PROTO||""))?"Symbol(src)_1."+Pe:"",qe=ne.toString,zi=RegExp("^"+We.call(lt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Be=Dt?tt.Buffer:void 0,oe=tt.Symbol,He=tt.Uint8Array,Ze=ne.propertyIsEnumerable,Ti=Ri.splice,It=oe?oe.toStringTag:void 0,Ge=Object.getOwnPropertySymbols,Wi=Be?Be.isBuffer:void 0,Ui=(ze=Object.keys,Te=Object,function(c){return ze(Te(c))}),be=Rt(tt,"DataView"),qt=Rt(tt,"Map"),Ce=Rt(tt,"Promise"),ke=Rt(tt,"Set"),we=Rt(tt,"WeakMap"),Bt=Rt(Object,"create"),qi=At(be),Bi=At(qt),Hi=At(Ce),Zi=At(ke),Gi=At(we),Ke=oe?oe.prototype:void 0,Me=Ke?Ke.valueOf:void 0;function St(c){var d=-1,g=c==null?0:c.length;for(this.clear();++d<g;){var v=c[d];this.set(v[0],v[1])}}function ht(c){var d=-1,g=c==null?0:c.length;for(this.clear();++d<g;){var v=c[d];this.set(v[0],v[1])}}function Nt(c){var d=-1,g=c==null?0:c.length;for(this.clear();++d<g;){var v=c[d];this.set(v[0],v[1])}}function re(c){var d=-1,g=c==null?0:c.length;for(this.__data__=new Nt;++d<g;)this.add(c[d])}function yt(c){var d=this.__data__=new ht(c);this.size=d.size}function Ki(c,d){var g=ce(c),v=!g&&tn(c),F=!g&&!v&&Ee(c),E=!g&&!v&&!F&&oi(c),R=g||v||F||E,P=R?function(W,ft){for(var pt=-1,Q=Array(W);++pt<W;)Q[pt]=ft(pt);return Q}(c.length,String):[],at=P.length;for(var T in c)!lt.call(c,T)||R&&(T=="length"||F&&(T=="offset"||T=="parent")||E&&(T=="buffer"||T=="byteLength"||T=="byteOffset")||Ji(T,at))||P.push(T);return P}function se(c,d){for(var g=c.length;g--;)if(ti(c[g][0],d))return g;return-1}function Ht(c){return c==null?c===void 0?J:M:It&&It in Object(c)?function(d){var g=lt.call(d,It),v=d[It];try{d[It]=void 0;var F=!0}catch{}var E=qe.call(d);return F&&(g?d[It]=v:delete d[It]),E}(c):function(d){return qe.call(d)}(c)}function Qe(c){return Zt(c)&&Ht(c)==s}function Xe(c,d,g,v,F){return c===d||(c==null||d==null||!Zt(c)&&!Zt(d)?c!=c&&d!=d:function(E,R,P,at,T,W){var ft=ce(E),pt=ce(R),Q=ft?a:vt(E),gt=pt?a:vt(R),Pt=(Q=Q==s?$:Q)==$,ue=(gt=gt==s?$:gt)==$,zt=Q==gt;if(zt&&Ee(E)){if(!Ee(R))return!1;ft=!0,Pt=!1}if(zt&&!Pt)return W||(W=new yt),ft||oi(E)?Ye(E,R,P,at,T,W):function(V,L,de,bt,xe,it,mt){switch(de){case Et:if(V.byteLength!=L.byteLength||V.byteOffset!=L.byteOffset)return!1;V=V.buffer,L=L.buffer;case rt:return!(V.byteLength!=L.byteLength||!it(new He(V),new He(L)));case p:case l:case I:return ti(+V,+L);case f:return V.name==L.name&&V.message==L.message;case X:case et:return V==L+"";case C:var Ct=Fi;case z:var Kt=bt&n;if(Ct||(Ct=Di),V.size!=L.size&&!Kt)return!1;var le=mt.get(V);if(le)return le==L;bt|=i,mt.set(V,L);var Oe=Ye(Ct(V),Ct(L),bt,xe,it,mt);return mt.delete(V),Oe;case K:if(Me)return Me.call(V)==Me.call(L)}return!1}(E,R,Q,P,at,T,W);if(!(P&n)){var Gt=Pt&&lt.call(E,"__wrapped__"),ri=ue&&lt.call(R,"__wrapped__");if(Gt||ri){var nn=Gt?E.value():E,on=ri?R.value():R;return W||(W=new yt),T(nn,on,P,at,W)}}return zt?(W||(W=new yt),function(V,L,de,bt,xe,it){var mt=de&n,Ct=Je(V),Kt=Ct.length,le=Je(L),Oe=le.length;if(Kt!=Oe&&!mt)return!1;for(var fe=Kt;fe--;){var jt=Ct[fe];if(!(mt?jt in L:lt.call(L,jt)))return!1}var si=it.get(V);if(si&&it.get(L))return si==L;var he=!0;it.set(V,L),it.set(L,V);for(var Ie=mt;++fe<Kt;){var pe=V[jt=Ct[fe]],ge=L[jt];if(bt)var ai=mt?bt(ge,pe,jt,L,V,it):bt(pe,ge,jt,V,L,it);if(!(ai===void 0?pe===ge||xe(pe,ge,de,bt,it):ai)){he=!1;break}Ie||(Ie=jt=="constructor")}if(he&&!Ie){var me=V.constructor,_e=L.constructor;me==_e||!("constructor"in V)||!("constructor"in L)||typeof me=="function"&&me instanceof me&&typeof _e=="function"&&_e instanceof _e||(he=!1)}return it.delete(V),it.delete(L),he}(E,R,P,at,T,W)):!1}(c,d,g,v,Xe,F))}function Qi(c){return!(!ni(c)||function(d){return!!Ue&&Ue in d}(c))&&(ei(c)?zi:ee).test(At(c))}function Xi(c){if(g=(d=c)&&d.constructor,v=typeof g=="function"&&g.prototype||ne,d!==v)return Ui(c);var d,g,v,F=[];for(var E in Object(c))lt.call(c,E)&&E!="constructor"&&F.push(E);return F}function Ye(c,d,g,v,F,E){var R=g&n,P=c.length,at=d.length;if(P!=at&&!(R&&at>P))return!1;var T=E.get(c);if(T&&E.get(d))return T==d;var W=-1,ft=!0,pt=g&i?new re:void 0;for(E.set(c,d),E.set(d,c);++W<P;){var Q=c[W],gt=d[W];if(v)var Pt=R?v(gt,Q,W,d,c,E):v(Q,gt,W,c,d,E);if(Pt!==void 0){if(Pt)continue;ft=!1;break}if(pt){if(!Vi(d,function(ue,zt){if(Gt=zt,!pt.has(Gt)&&(Q===ue||F(Q,ue,g,v,E)))return pt.push(zt);var Gt})){ft=!1;break}}else if(Q!==gt&&!F(Q,gt,g,v,E)){ft=!1;break}}return E.delete(c),E.delete(d),ft}function Je(c){return function(d,g,v){var F=g(d);return ce(d)?F:function(E,R){for(var P=-1,at=R.length,T=E.length;++P<at;)E[T+P]=R[P];return E}(F,v(d))}(c,en,Yi)}function ae(c,d){var g,v,F=c.__data__;return((v=typeof(g=d))=="string"||v=="number"||v=="symbol"||v=="boolean"?g!=="__proto__":g===null)?F[typeof d=="string"?"string":"hash"]:F.map}function Rt(c,d){var g=function(v,F){return v==null?void 0:v[F]}(c,d);return Qi(g)?g:void 0}St.prototype.clear=function(){this.__data__=Bt?Bt(null):{},this.size=0},St.prototype.delete=function(c){var d=this.has(c)&&delete this.__data__[c];return this.size-=d?1:0,d},St.prototype.get=function(c){var d=this.__data__;if(Bt){var g=d[c];return g===e?void 0:g}return lt.call(d,c)?d[c]:void 0},St.prototype.has=function(c){var d=this.__data__;return Bt?d[c]!==void 0:lt.call(d,c)},St.prototype.set=function(c,d){var g=this.__data__;return this.size+=this.has(c)?0:1,g[c]=Bt&&d===void 0?e:d,this},ht.prototype.clear=function(){this.__data__=[],this.size=0},ht.prototype.delete=function(c){var d=this.__data__,g=se(d,c);return!(g<0)&&(g==d.length-1?d.pop():Ti.call(d,g,1),--this.size,!0)},ht.prototype.get=function(c){var d=this.__data__,g=se(d,c);return g<0?void 0:d[g][1]},ht.prototype.has=function(c){return se(this.__data__,c)>-1},ht.prototype.set=function(c,d){var g=this.__data__,v=se(g,c);return v<0?(++this.size,g.push([c,d])):g[v][1]=d,this},Nt.prototype.clear=function(){this.size=0,this.__data__={hash:new St,map:new(qt||ht),string:new St}},Nt.prototype.delete=function(c){var d=ae(this,c).delete(c);return this.size-=d?1:0,d},Nt.prototype.get=function(c){return ae(this,c).get(c)},Nt.prototype.has=function(c){return ae(this,c).has(c)},Nt.prototype.set=function(c,d){var g=ae(this,c),v=g.size;return g.set(c,d),this.size+=g.size==v?0:1,this},re.prototype.add=re.prototype.push=function(c){return this.__data__.set(c,e),this},re.prototype.has=function(c){return this.__data__.has(c)},yt.prototype.clear=function(){this.__data__=new ht,this.size=0},yt.prototype.delete=function(c){var d=this.__data__,g=d.delete(c);return this.size=d.size,g},yt.prototype.get=function(c){return this.__data__.get(c)},yt.prototype.has=function(c){return this.__data__.has(c)},yt.prototype.set=function(c,d){var g=this.__data__;if(g instanceof ht){var v=g.__data__;if(!qt||v.length<199)return v.push([c,d]),this.size=++g.size,this;g=this.__data__=new Nt(v)}return g.set(c,d),this.size=g.size,this};var Yi=Ge?function(c){return c==null?[]:(c=Object(c),function(d,g){for(var v=-1,F=d==null?0:d.length,E=0,R=[];++v<F;){var P=d[v];g(P,v,d)&&(R[E++]=P)}return R}(Ge(c),function(d){return Ze.call(c,d)}))}:function(){return[]},vt=Ht;function Ji(c,d){return!!(d=d??r)&&(typeof c=="number"||st.test(c))&&c>-1&&c%1==0&&c<d}function At(c){if(c!=null){try{return We.call(c)}catch{}try{return c+""}catch{}}return""}function ti(c,d){return c===d||c!=c&&d!=d}(be&&vt(new be(new ArrayBuffer(1)))!=Et||qt&&vt(new qt)!=C||Ce&&vt(Ce.resolve())!=k||ke&&vt(new ke)!=z||we&&vt(new we)!=D)&&(vt=function(c){var d=Ht(c),g=d==$?c.constructor:void 0,v=g?At(g):"";if(v)switch(v){case qi:return Et;case Bi:return C;case Hi:return k;case Zi:return z;case Gi:return D}return d});var tn=Qe(function(){return arguments}())?Qe:function(c){return Zt(c)&&lt.call(c,"callee")&&!Ze.call(c,"callee")},ce=Array.isArray,Ee=Wi||function(){return!1};function ei(c){if(!ni(c))return!1;var d=Ht(c);return d==h||d==m||d==u||d==B}function ii(c){return typeof c=="number"&&c>-1&&c%1==0&&c<=r}function ni(c){var d=typeof c;return c!=null&&(d=="object"||d=="function")}function Zt(c){return c!=null&&typeof c=="object"}var oi=Re?function(c){return function(d){return c(d)}}(Re):function(c){return Zt(c)&&ii(c.length)&&!!j[Ht(c)]};function en(c){return(d=c)!=null&&ii(d.length)&&!ei(d)?Ki(c):Xi(c);var d}o.exports=function(c,d){return Xe(c,d)}})(Ve,Ve.exports);const Fn=an(Ve.exports);function Li(o){return function(t){return"unitOfCodeWork"in t&&!function(e){return e.children.length>0&&"childIds"in e}(t)}(o)?[o]:o.children.flatMap(Li)}var Mt=(o=>(o.edit="edit",o.instruction="instruction",o))(Mt||{}),Fe=(o=>(o[o.instructionDrawer=0]="instructionDrawer",o[o.chunkActionPanel=1]="chunkActionPanel",o))(Fe||{});class Dn{constructor(t,e,n){_(this,"_originalModel");_(this,"_modifiedModel");_(this,"_fullEdits",[]);_(this,"_currEdit");_(this,"_currOriginalEdit");_(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(e=>{this._modifiedModel.applyEdits([e])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});_(this,"finish",()=>this._completeCurrEdit());_(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);_(this,"_completeCurrEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};if(!t)return e;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const n=this._nextModifiedInsertPosition(),i=t.stagedEndLine-t.stagedStartLine,r={range:new this._monaco.Range(n.lineNumber,0,n.lineNumber+i,0),text:""};e.modified.push(r),this._modifiedModel.applyEdits([r]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return e});_(this,"_startNewEdit",t=>{const e={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},e.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),e});_(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const e=this._nextModifiedInsertPosition(),n={...this._currEdit,text:t.newText,range:new this._monaco.Range(e.lineNumber,e.column,e.lineNumber,e.column)};return this._modifiedModel.applyEdits([n]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[n]:[]}});_(this,"_nextModifiedInsertPosition",()=>{var e;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((e=this._currEdit.text)==null?void 0:e.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=e,this._monaco=n,this._originalModel=this._monaco.editor.createModel(e),this._modifiedModel=this._monaco.editor.createModel(e)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class Rn{constructor(t,e,n){_(this,"_asyncMsgSender");_(this,"_editor");_(this,"_chatModel");_(this,"_focusModel",new xn);_(this,"_hasScrolledOnInit",!1);_(this,"_markHasScrolledOnInit",fi(()=>{this._hasScrolledOnInit=!0},200));_(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});_(this,"_subscribers",new Set);_(this,"_disposables",[]);_(this,"_rootChunk");_(this,"_keybindings",Lt({}));_(this,"_requestId",Lt(void 0));_(this,"requestId",this._requestId);_(this,"_disableResolution",Lt(!1));_(this,"disableResolution",ci(this._disableResolution));_(this,"_disableApply",Lt(!1));_(this,"disableApply",ci(this._disableApply));_(this,"_currStream");_(this,"_isLoadingDiffChunks",Lt(!1));_(this,"_selectionLines",Lt(void 0));_(this,"_mode",Lt(Mt.edit));_(this,"initializeEditor",t=>{var e,n,i,r,s,a,u,p,l,f,h,m;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Si(new Ai(Xt),Xt,new Ni),(n=(e=this._monaco.editor).registerCommand)==null||n.call(e,"acceptFocusedChunk",this.acceptFocusedChunk),(r=(i=this._monaco.editor).registerCommand)==null||r.call(i,"rejectFocusedChunk",this.rejectFocusedChunk),(a=(s=this._monaco.editor).registerCommand)==null||a.call(s,"acceptAllChunks",this.acceptAllChunks),(p=(u=this._monaco.editor).registerCommand)==null||p.call(u,"rejectAllChunks",this.rejectAllChunks),(f=(l=this._monaco.editor).registerCommand)==null||f.call(l,"focusNextChunk",this.focusNextChunk),(m=(h=this._monaco.editor).registerCommand)==null||m.call(h,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(C=>this.notifySubscribers())}),this.initialize()});_(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));_(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});_(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});_(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const e=this.leaves[0];this.revealChunk(e)}this.notifyDiffViewUpdated(),this.notifySubscribers()});_(this,"onMouseMoveModified",t=>{var i,r,s,a,u,p;if(((i=t.target.position)==null?void 0:i.lineNumber)===void 0||this.leaves===void 0)return;const e=this.editorOffset,n=(r=t.target.position)==null?void 0:r.lineNumber;for(let l=0;l<this.leaves.length;l++){const f=this.leaves[l],h=(s=f.unitOfCodeWork.lineChanges)==null?void 0:s.lineChanges[0].modifiedStart,m=(a=f.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedEnd,C=(u=f.unitOfCodeWork.lineChanges)==null?void 0:u.lineChanges[0].originalStart,I=(p=f.unitOfCodeWork.lineChanges)==null?void 0:p.lineChanges[0].originalEnd;if(h!==void 0&&m!==void 0&&C!==void 0&&I!==void 0){if(h!==m||n!==h){if(h<=n&&n<m){this.setCurrFocusedChunkIdx(l,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const M=this._editor.getOriginalEditor(),$=M.getOption(this._monaco.editor.EditorOption.lineHeight),k=M.getScrolledVisiblePosition({lineNumber:C,column:0}),B=M.getScrolledVisiblePosition({lineNumber:I+1,column:0});if(k===null||B===null)continue;const X=k.top-$/2+e,z=B.top-$/2+e;if(t.event.posy>=X&&t.event.posy<=z){this.setCurrFocusedChunkIdx(l,!1);break}break}}}});_(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:Y.diffViewWindowFocusChange,data:t})});_(this,"setCurrFocusedChunkIdx",(t,e=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),e&&this.revealCurrFocusedChunk(),this.notifySubscribers())});_(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});_(this,"revealChunk",t=>{var i;const e=(i=t.unitOfCodeWork.lineChanges)==null?void 0:i.lineChanges[0],n=e==null?void 0:e.modifiedStart;n!==void 0&&this._editor.revealLineNearTop(n-1)});_(this,"renderCentralOverlayWidget",t=>{const e=()=>({editor:this._editor,id:"central-overlay-widget"}),n=En(t,e(),{monaco:this._monaco});return{update:()=>{n.update(e())},destroy:n.destroy}});_(this,"renderInstructionsDrawerViewZone",(t,e)=>{let n=!1,i=e;const r=e.autoFocus??!0,s=l=>{r&&!n&&(this._editor.revealLineNearTop(l),n=!0)},a=l=>({...l,ordinal:Fe.instructionDrawer,editor:this._editor,afterLineNumber:l.line}),u=hi(t,a(e)),p=[];return r&&p.push(this._editor.onDidUpdateDiff(()=>{s(i.line)})),{update:l=>{const f={...i,...l};Fn(f,i)||(u.update(a(f)),i=f,s(f.line))},destroy:()=>{u.destroy(),p.forEach(l=>l.dispose())}}});_(this,"renderActionsViewZone",(t,e)=>{const n=r=>{var a;let s;return s=r.chunk?(a=r.chunk.unitOfCodeWork.lineChanges)==null?void 0:a.lineChanges[0].modifiedStart:1,{...r,ordinal:Fe.chunkActionPanel,editor:this._editor,afterLineNumber:s?s-1:void 0}},i=hi(t,n(e));return{update:r=>{i.update(n(r))},destroy:i.destroy}});_(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});_(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});_(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});_(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});_(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});_(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});_(this,"initialize",async()=>{var u;const t=await this._asyncMsgSender.send({type:Y.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:e,instruction:n,keybindings:i,editable:r}=t.data;this._editor.updateOptions({readOnly:!r});const s=ct(this._keybindings);this._keybindings.set(i??s);const a=n==null?void 0:n.selection;a&&(a.start.line===a.end.line&&a.start.character===a.end.character&&this._mode.set(Mt.instruction),ct(this.selectionLines)===void 0&&this._selectionLines.set({start:a.start.line,end:a.end.line})),this.updateModels(e.originalCode??"",e.modifiedCode??"",{rootPath:e.repoRoot,relPath:e.pathName}),(u=this._currStream)==null||u.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});_(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:Y.disposeDiffView})});_(this,"_tryFetchStream",async()=>{var e,n,i;const t=this._asyncMsgSender.stream({type:Y.diffViewFetchPendingStream},15e3,6e4);for await(const r of t)switch(r.type){case Y.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(r.data.requestId);const s=this._editor.getOriginalEditor().getValue();this._currStream=new Dn(r.data.streamId,s,this._monaco),this._syncStreamToModels();break}case Y.diffViewDiffStreamEnded:if(((e=this._currStream)==null?void 0:e.id)!==r.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case Y.diffViewDiffStreamChunk:{if(((n=this._currStream)==null?void 0:n.id)!==r.data.streamId)return;const s=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!s)return this.setLoading(!1),void this._cleanupStream();const a=(i=this._currStream)==null?void 0:i.onReceiveChunk(r);a&&(this._applyDeltaDiff(a),ct(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});_(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case Y.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case Y.diffViewAcceptAllChunks:this.acceptAllChunks();break;case Y.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case Y.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case Y.diffViewFocusPrevChunk:this.focusPrevChunk();break;case Y.diffViewFocusNextChunk:this.focusNextChunk()}});_(this,"_applyDeltaDiff",t=>{const e=this._editor.getOriginalEditor().getModel(),n=this._editor.getModifiedEditor().getModel();e&&n&&(e.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(i=>{e.pushEditOperations([],[i],()=>[])}),t.modified.forEach(i=>{n.pushEditOperations([],[i],()=>[])}))});_(this,"_cleanupStream",()=>{var t;if(this._currStream){const e=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(e),this._currStream=void 0,this._resetScrollOnInit()}});_(this,"_syncStreamToModels",()=>{var n,i;const t=(n=this._currStream)==null?void 0:n.originalValue,e=(i=this._currStream)==null?void 0:i.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),e&&e!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(e)});_(this,"acceptChunk",async t=>{ct(this._disableApply)||this.acceptChunks([t])});_(this,"acceptChunks",async(t,e=!1)=>{ct(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Se.accept,e),await Ae(),this.areModelsEqual()&&!ct(this.isLoading)&&this.disposeDiffViewPanel())});_(this,"areModelsEqual",()=>{var n,i;const t=(n=this._editor.getModel())==null?void 0:n.original,e=(i=this._editor.getModel())==null?void 0:i.modified;return(t==null?void 0:t.getValue())===(e==null?void 0:e.getValue())});_(this,"rejectChunk",async t=>{this.rejectChunks([t])});_(this,"rejectChunks",async(t,e=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Se.reject,e),await Ae(),this.areModelsEqual()&&!ct(this.isLoading)&&this.disposeDiffViewPanel()});_(this,"notifyDiffViewUpdated",fi(()=>{this.notifyResolvedChunks([],Se.accept)},1e3));_(this,"notifyResolvedChunks",async(t,e,n=!1)=>{var r;const i=(r=this._editor.getModel())==null?void 0:r.original.uri.path;i&&await this._asyncMsgSender.send({type:Y.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:i,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(s=>s.unitOfCodeWork),resolveType:e,shouldApplyToAll:n}},2e3)});_(this,"executeDiffChunks",(t,e)=>{var l,f,h;if(ct(this._disableResolution)||e&&ct(this._disableApply))return;const n=(l=this._editor.getModel())==null?void 0:l.original,i=(f=this._editor.getModel())==null?void 0:f.modified;if(!n||!i||this._currStream!==void 0)return;const r=[],s=[];for(const m of t){const C=(h=m.unitOfCodeWork.lineChanges)==null?void 0:h.lineChanges[0];if(!C||m.unitOfCodeWork.originalCode===void 0||m.unitOfCodeWork.modifiedCode===void 0)continue;let I={startLineNumber:C.originalStart,startColumn:1,endLineNumber:C.originalEnd,endColumn:1},M={startLineNumber:C.modifiedStart,startColumn:1,endLineNumber:C.modifiedEnd,endColumn:1};const $=e?m.unitOfCodeWork.modifiedCode:m.unitOfCodeWork.originalCode;$!==void 0&&(r.push({range:I,text:$}),s.push({range:M,text:$}))}n.pushEditOperations([],r,()=>[]),i.pushEditOperations([],s,()=>[]);const a=this._focusModel.nextIdx({nowrap:!0});if(a===void 0)return;const u=a===this._focusModel.focusedItemIdx?a-1:a,p=this._focusModel.items[u];p&&this.revealChunk(p)});_(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});_(this,"handleInstructionSubmit",t=>{const e=this._editor.getModifiedEditor(),n=this.getSelectedCodeDetails(e);if(!n)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,n)});_(this,"updateModels",(t,e,n)=>{var s,a;const i=(a=(s=this._editor.getModel())==null?void 0:s.original)==null?void 0:a.uri,r=(n&&this._monaco.Uri.file(n.relPath))??i;if(r)if((i==null?void 0:i.fsPath)!==r.fsPath||(i==null?void 0:i.authority)!==r.authority){const u=r.with({fragment:crypto.randomUUID()}),p=r.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,u),modified:this._monaco.editor.createModel(e??"",void 0,p)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==e&&this.getModifiedEditor().setValue(e??"");else console.warn("No URI found for diff view. Not updating models.")});_(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=n,this._asyncMsgSender=new Cn(i=>Xt.postMessage(i)),this.initializeEditor(e)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Li(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var r,s;const t=[],e=this._editor.getLineChanges(),n=(r=this._editor.getModel())==null?void 0:r.original,i=(s=this._editor.getModel())==null?void 0:s.modified;if(e&&n&&i){for(const a of e){const u=pi({startLineNumber:a.originalStartLineNumber,endLineNumber:a.originalEndLineNumber}),p=pi({startLineNumber:a.modifiedStartLineNumber,endLineNumber:a.modifiedEndLineNumber}),l=Pn(this._editor,u,p);t.push(l)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(a=>a.id)}}}getSelectedCodeDetails(t){const e=t.getModel();if(!e)return null;const n=e.getLanguageId(),i=1,r=1,s={lineNumber:e.getLineCount(),column:e.getLineMaxColumn(e.getLineCount())},a=ct(this._selectionLines);if(!a)throw new Error("No selection lines found");const u=Math.min(a.end+1,s.lineNumber),p=new this._monaco.Range(a.start+1,1,u,e.getLineMaxColumn(u));let l=e.getValueInRange(p);u<e.getLineCount()&&(l+=e.getEOL());const f=new this._monaco.Range(i,r,p.startLineNumber,p.startColumn),h=Math.min(p.endLineNumber+1,s.lineNumber),m=new this._monaco.Range(h,1,s.lineNumber,s.column);return{selectedCode:l,prefix:e.getValueInRange(f),suffix:e.getValueInRange(m),path:e.uri.path,language:n,prefixBegin:f.startLineNumber-1,suffixEnd:m.endLineNumber-1}}}function Pn(o,t,e){var r,s;const n=(r=o.getModel())==null?void 0:r.original,i=(s=o.getModel())==null?void 0:s.modified;if(!n||!i)throw new Error("No models found");return function(a,u,p,l){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:a,modifiedCode:u,lineChanges:{lineChanges:[{originalStart:p.startLineNumber,originalEnd:p.endLineNumber,modifiedStart:l.startLineNumber,modifiedEnd:l.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(n.getValueInRange(t),i.getValueInRange(e),t,e)}function pi(o){return o.endLineNumber===0?{startLineNumber:o.startLineNumber+1,startColumn:1,endLineNumber:o.startLineNumber+1,endColumn:1}:{startLineNumber:o.startLineNumber,startColumn:1,endLineNumber:o.endLineNumber+1,endColumn:1}}function gi(o){let t,e;return t=new wt({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[zn]},$$scope:{ctx:o}}}),t.$on("click",function(){$t(o[4])&&o[4].apply(this,arguments)}),{c(){A(t.$$.fragment)},m(n,i){N(t,n,i),e=!0},p(n,i){o=n;const r={};132096&i&&(r.$$scope={dirty:i,ctx:o}),t.$set(r)},i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){S(t,n)}}}function zn(o){let t,e,n;return t=new Vt({props:{keybinding:o[10].acceptFocusedChunk}}),{c(){A(t.$$.fragment),e=ot(`
        Accept`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};1024&r&&(s.keybinding=i[10].acceptFocusedChunk),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function Tn(o){let t,e,n;return t=new Vt({props:{keybinding:o[10].rejectFocusedChunk}}),{c(){A(t.$$.fragment),e=ot(`
      Reject`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};1024&r&&(s.keybinding=i[10].rejectFocusedChunk),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function Wn(o){let t,e,n,i,r,s,a,u,p,l,f=!o[3]&&gi(o);return a=new wt({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Tn]},$$scope:{ctx:o}}}),a.$on("click",function(){$t(o[5])&&o[5].apply(this,arguments)}),{c(){t=H("div"),n=Z(),i=H("div"),r=H("div"),f&&f.c(),s=Z(),A(a.$$.fragment),q(t,"class","svelte-zm1705"),nt(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),q(r,"class","c-button-container svelte-zm1705"),nt(r,"c-button-container--focused",o[1]),nt(r,"c-button-container--transparent",o[9]),q(i,"class","c-chunk-action-panel-anchor svelte-zm1705"),Tt(i,"top",o[8]+"px"),nt(i,"c-chunk-action-panel-anchor--left",o[0]==="left"),nt(i,"c-chunk-action-panel-anchor--right",o[0]==="right"),nt(i,"c-chunk-action-panel-anchor--focused",o[1])},m(h,m){O(h,t,m),O(h,n,m),O(h,i,m),U(i,r),f&&f.m(r,null),U(r,s),N(a,r,null),u=!0,p||(l=[Ei(e=o[6].renderActionsViewZone(t,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]})),kt(i,"mouseenter",o[13]),kt(i,"mousemove",o[13]),kt(i,"mouseleave",o[13])],p=!0)},p(h,[m]){o=h,e&&$t(e.update)&&132&m&&e.update.call(null,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]}),(!u||130&m)&&nt(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),o[3]?f&&(ut(),b(f,1,1,()=>{f=null}),dt()):f?(f.p(o,m),8&m&&y(f,1)):(f=gi(o),f.c(),y(f,1),f.m(r,s));const C={};132096&m&&(C.$$scope={dirty:m,ctx:o}),a.$set(C),(!u||2&m)&&nt(r,"c-button-container--focused",o[1]),(!u||512&m)&&nt(r,"c-button-container--transparent",o[9]),(!u||256&m)&&Tt(i,"top",o[8]+"px"),(!u||1&m)&&nt(i,"c-chunk-action-panel-anchor--left",o[0]==="left"),(!u||1&m)&&nt(i,"c-chunk-action-panel-anchor--right",o[0]==="right"),(!u||2&m)&&nt(i,"c-chunk-action-panel-anchor--focused",o[1])},i(h){u||(y(f),y(a.$$.fragment,h),u=!0)},o(h){b(f),b(a.$$.fragment,h),u=!1},d(h){h&&(x(t),x(n),x(i)),f&&f.d(),S(a),p=!1,De(l)}}}function Un(o,t,e){let n,{align:i="right"}=t,{isFocused:r}=t,{heightInPx:s=1}=t,{disableApply:a=!1}=t,{onAccept:u}=t,{onReject:p}=t,{diffViewModel:l}=t,{leaf:f}=t;const h=l.keybindings;Wt(o,h,$=>e(10,n=$));let m=0,C,I=!1;function M(){C&&(clearTimeout(C),C=void 0),e(9,I=!1)}return o.$$set=$=>{"align"in $&&e(0,i=$.align),"isFocused"in $&&e(1,r=$.isFocused),"heightInPx"in $&&e(2,s=$.heightInPx),"disableApply"in $&&e(3,a=$.disableApply),"onAccept"in $&&e(4,u=$.onAccept),"onReject"in $&&e(5,p=$.onReject),"diffViewModel"in $&&e(6,l=$.diffViewModel),"leaf"in $&&e(7,f=$.leaf)},[i,r,s,a,u,p,l,f,m,I,n,h,$=>{e(8,m=$)},function($){$.target.closest(".c-button-container")?M():$.type==="mouseenter"||$.type==="mousemove"?(M(),C=setTimeout(()=>{e(9,I=!0)},400)):$.type==="mouseleave"&&M()}]}class qn extends Yt{constructor(t){super(),Jt(this,t,Un,Wn,te,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function mi(o){let t,e,n;function i(s){o[18](s)}let r={onOpenChange:o[16],content:o[3],triggerOn:[kn.Hover],$$slots:{default:[Hn]},$$scope:{ctx:o}};return o[4]!==void 0&&(r.requestClose=o[4]),t=new wn({props:r}),Qt.push(()=>cn(t,"requestClose",i)),{c(){A(t.$$.fragment)},m(s,a){N(t,s,a),n=!0},p(s,a){const u={};8&a&&(u.content=s[3]),1048576&a&&(u.$$scope={dirty:a,ctx:s}),!e&&16&a&&(e=!0,u.requestClose=s[4],un(()=>e=!1)),t.$set(u)},i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){b(t.$$.fragment,s),n=!1},d(s){S(t,s)}}}function Bn(o){let t,e;return t=new Mn({}),{c(){A(t.$$.fragment)},m(n,i){N(t,n,i),e=!0},i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){S(t,n)}}}function Hn(o){let t,e;return t=new vn({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Bn]},$$scope:{ctx:o}}}),t.$on("click",o[17]),{c(){A(t.$$.fragment)},m(n,i){N(t,n,i),e=!0},p(n,i){const r={};1048576&i&&(r.$$scope={dirty:i,ctx:n}),t.$set(r)},i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){S(t,n)}}}function Zn(o){let t;return{c(){t=H("span"),t.textContent="No changes",q(t,"class","c-diff-page-counter svelte-1w94ymh")},m(e,n){O(e,t,n)},p:G,i:G,o:G,d(e){e&&x(t)}}}function Gn(o){var p,l;let t,e,n,i,r,s,a,u=((l=(p=o[1])==null?void 0:p.leaves)==null?void 0:l.length)+"";return s=new xi({props:{size:1,loading:o[10]}}),{c(){t=H("span"),e=ot(o[2]),n=ot(" of "),i=ot(u),r=Z(),A(s.$$.fragment),q(t,"class","c-diff-page-counter svelte-1w94ymh")},m(f,h){O(f,t,h),U(t,e),U(t,n),U(t,i),U(t,r),N(s,t,null),a=!0},p(f,h){var C,I;(!a||4&h)&&je(e,f[2]),(!a||2&h)&&u!==(u=((I=(C=f[1])==null?void 0:C.leaves)==null?void 0:I.length)+"")&&je(i,u);const m={};1024&h&&(m.loading=f[10]),s.$set(m)},i(f){a||(y(s.$$.fragment,f),a=!0)},o(f){b(s.$$.fragment,f),a=!1},d(f){f&&x(t),S(s)}}}function Kn(o){let t,e,n,i;return n=new xi({props:{size:1,loading:o[10]}}),{c(){t=H("span"),e=ot(`Generating changes
        `),A(n.$$.fragment),q(t,"class","c-diff-page-counter svelte-1w94ymh")},m(r,s){O(r,t,s),U(t,e),N(n,t,null),i=!0},p(r,s){const a={};1024&s&&(a.loading=r[10]),n.$set(a)},i(r){i||(y(n.$$.fragment,r),i=!0)},o(r){b(n.$$.fragment,r),i=!1},d(r){r&&x(t),S(n)}}}function _i(o){let t,e,n,i,r,s;t=new wt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Qn]},$$scope:{ctx:o}}}),t.$on("click",function(){$t(o[0].focusPrevChunk)&&o[0].focusPrevChunk.apply(this,arguments)}),n=new wt({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Xn]},$$scope:{ctx:o}}}),n.$on("click",function(){$t(o[0].focusNextChunk)&&o[0].focusNextChunk.apply(this,arguments)});let a=!o[12]&&$i(o);return{c(){A(t.$$.fragment),e=Z(),A(n.$$.fragment),i=Z(),a&&a.c(),r=ye()},m(u,p){N(t,u,p),O(u,e,p),N(n,u,p),O(u,i,p),a&&a.m(u,p),O(u,r,p),s=!0},p(u,p){o=u;const l={};1050624&p&&(l.$$scope={dirty:p,ctx:o}),t.$set(l);const f={};1050624&p&&(f.$$scope={dirty:p,ctx:o}),n.$set(f),o[12]?a&&(ut(),b(a,1,1,()=>{a=null}),dt()):a?(a.p(o,p),4096&p&&y(a,1)):(a=$i(o),a.c(),y(a,1),a.m(r.parentNode,r))},i(u){s||(y(t.$$.fragment,u),y(n.$$.fragment,u),y(a),s=!0)},o(u){b(t.$$.fragment,u),b(n.$$.fragment,u),b(a),s=!1},d(u){u&&(x(e),x(i),x(r)),S(t,u),S(n,u),a&&a.d(u)}}}function Qn(o){let t,e,n;return t=new Vt({props:{keybinding:o[11].focusPrevChunk}}),{c(){A(t.$$.fragment),e=ot(`
        Back`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};2048&r&&(s.keybinding=i[11].focusPrevChunk),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function Xn(o){let t,e,n;return t=new Vt({props:{keybinding:o[11].focusNextChunk}}),{c(){A(t.$$.fragment),e=ot(`
        Next`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};2048&r&&(s.keybinding=i[11].focusNextChunk),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function $i(o){let t,e,n,i=!o[13]&&yi(o);return e=new wt({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Jn]},$$scope:{ctx:o}}}),e.$on("click",function(){$t(o[0].rejectAllChunks)&&o[0].rejectAllChunks.apply(this,arguments)}),{c(){i&&i.c(),t=Z(),A(e.$$.fragment)},m(r,s){i&&i.m(r,s),O(r,t,s),N(e,r,s),n=!0},p(r,s){(o=r)[13]?i&&(ut(),b(i,1,1,()=>{i=null}),dt()):i?(i.p(o,s),8192&s&&y(i,1)):(i=yi(o),i.c(),y(i,1),i.m(t.parentNode,t));const a={};1050624&s&&(a.$$scope={dirty:s,ctx:o}),e.$set(a)},i(r){n||(y(i),y(e.$$.fragment,r),n=!0)},o(r){b(i),b(e.$$.fragment,r),n=!1},d(r){r&&x(t),i&&i.d(r),S(e,r)}}}function yi(o){let t,e;return t=new wt({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Yn]},$$scope:{ctx:o}}}),t.$on("click",function(){$t(o[0].acceptAllChunks)&&o[0].acceptAllChunks.apply(this,arguments)}),{c(){A(t.$$.fragment)},m(n,i){N(t,n,i),e=!0},p(n,i){o=n;const r={};1050624&i&&(r.$$scope={dirty:i,ctx:o}),t.$set(r)},i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){S(t,n)}}}function Yn(o){let t,e,n;return t=new Vt({props:{keybinding:o[11].acceptAllChunks}}),{c(){A(t.$$.fragment),e=ot(`
            Accept All`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};2048&r&&(s.keybinding=i[11].acceptAllChunks),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function Jn(o){let t,e,n;return t=new Vt({props:{keybinding:o[11].rejectAllChunks}}),{c(){A(t.$$.fragment),e=ot(`
          Reject All`)},m(i,r){N(t,i,r),O(i,e,r),n=!0},p(i,r){const s={};2048&r&&(s.keybinding=i[11].rejectAllChunks),t.$set(s)},i(i){n||(y(t.$$.fragment,i),n=!0)},o(i){b(t.$$.fragment,i),n=!1},d(i){i&&x(e),S(t,i)}}}function to(o){let t,e,n,i,r,s,a,u=o[9]&&mi(o);const p=[Kn,Gn,Zn],l=[];function f(m,C){return!m[5]&&m[10]?0:m[5]?1:2}i=f(o),r=l[i]=p[i](o);let h=o[5]&&_i(o);return{c(){t=H("div"),e=H("div"),u&&u.c(),n=Z(),r.c(),s=Z(),h&&h.c(),q(e,"class","c-button-container svelte-1w94ymh"),q(t,"class","c-top-action-panel-anchor svelte-1w94ymh")},m(m,C){O(m,t,C),U(t,e),u&&u.m(e,null),U(e,n),l[i].m(e,null),U(e,s),h&&h.m(e,null),a=!0},p(m,[C]){m[9]?u?(u.p(m,C),512&C&&y(u,1)):(u=mi(m),u.c(),y(u,1),u.m(e,n)):u&&(ut(),b(u,1,1,()=>{u=null}),dt());let I=i;i=f(m),i===I?l[i].p(m,C):(ut(),b(l[I],1,1,()=>{l[I]=null}),dt(),r=l[i],r?r.p(m,C):(r=l[i]=p[i](m),r.c()),y(r,1),r.m(e,s)),m[5]?h?(h.p(m,C),32&C&&y(h,1)):(h=_i(m),h.c(),y(h,1),h.m(e,null)):h&&(ut(),b(h,1,1,()=>{h=null}),dt())},i(m){a||(y(u),y(r),y(h),a=!0)},o(m){b(u),b(r),b(h),a=!1},d(m){m&&x(t),u&&u.d(),l[i].d(),h&&h.d()}}}function eo(o,t,e){let n,i,r,s,a,u,p,l,f,h,m=G,C=()=>(m(),m=_t(k,D=>e(1,u=D)),k),I=G,M=G,$=G;o.$$.on_destroy.push(()=>m()),o.$$.on_destroy.push(()=>I()),o.$$.on_destroy.push(()=>M()),o.$$.on_destroy.push(()=>$());let{diffViewModel:k}=t;C();const B=k.keybindings;Wt(o,B,D=>e(11,l=D));const X=k.requestId;Wt(o,X,D=>e(9,a=D));let z,et="x",K="Copy request ID",J=()=>{};return o.$$set=D=>{"diffViewModel"in D&&C(e(0,k=D.diffViewModel))},o.$$.update=()=>{var D;2&o.$$.dirty&&(e(8,n=u.disableResolution),M(),M=_t(n,rt=>e(12,f=rt))),2&o.$$.dirty&&(e(7,i=u.disableApply),$(),$=_t(i,rt=>e(13,h=rt))),2&o.$$.dirty&&(u.currFocusedChunkIdx!==void 0?e(2,et=(u.currFocusedChunkIdx+1).toString()):e(2,et="x")),2&o.$$.dirty&&(e(6,r=u.isLoading),I(),I=_t(r,rt=>e(10,p=rt))),2&o.$$.dirty&&e(5,s=!!((D=u.leaves)!=null&&D.length))},[k,u,et,K,J,s,r,i,n,a,p,l,f,h,B,X,function(D){D||(clearTimeout(z),z=void 0,e(3,K="Copy request ID"))},async function(){a&&(await navigator.clipboard.writeText(a),e(3,K="Copied!"),clearTimeout(z),z=setTimeout(J,1500))},function(D){J=D,e(4,J)}]}class io extends Yt{constructor(t){super(),Jt(this,t,eo,to,te,{diffViewModel:0})}}function no(o){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},o[0]],i={};for(let r=0;r<n.length;r+=1)i=Le(i,n[r]);return{c(){t=gn("svg"),e=new mn(!0),this.h()},l(r){t=fn(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var s=hn(t);e=pn(s,!0),s.forEach(x),this.h()},h(){e.a=null,ui(t,i)},m(r,s){ln(r,t,s),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',t)},p(r,[s]){ui(t,i=dn(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&s&&r[0]]))},i:G,o:G,d(r){r&&x(t)}}}function oo(o,t,e){return o.$$set=n=>{e(0,t=Le(Le({},t),di(n)))},[t=di(t)]}class ro extends Yt{constructor(t){super(),Jt(this,t,oo,no,te,{})}}function vi(o){let t,e,n,i,r,s,a,u,p,l,f,h,m,C,I={focusOnInit:!0,$$slots:{default:[so]},$$scope:{ctx:o}};return s=new ji.Root({props:I}),o[25](s),p=new wt({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[ao]},$$scope:{ctx:o}}}),p.$on("click",function(){$t(o[0].disposeDiffViewPanel)&&o[0].disposeDiffViewPanel.apply(this,arguments)}),f=new wt({props:{id:"send",size:1,variant:"solid",color:"accent",title:o[3]===Mt.instruction?"Instruct Augment":"Edit with Augment",disabled:!o[4].trim()||o[11],$$slots:{iconRight:[uo],default:[co]},$$scope:{ctx:o}}}),f.$on("click",o[14]),{c(){t=H("div"),e=Z(),n=H("div"),i=H("div"),r=H("div"),A(s.$$.fragment),a=Z(),u=H("div"),A(p.$$.fragment),l=Z(),A(f.$$.fragment),q(r,"class","l-input-area__input svelte-1cxscce"),q(u,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),q(i,"class","instruction-drawer-panel__contents svelte-1cxscce"),q(i,"tabindex","0"),q(i,"role","button"),q(n,"class","instruction-drawer-panel svelte-1cxscce"),Tt(n,"top",o[5]+"px"),Tt(n,"height",o[6]+"px")},m(M,$){O(M,t,$),O(M,e,$),O(M,n,$),U(n,i),U(i,r),N(s,r,null),o[26](r),U(i,a),U(i,u),N(p,u,null),U(u,l),N(f,u,null),h=!0,m||(C=[Ei(o[15].call(null,t)),kt(i,"click",o[17]),kt(i,"keydown",o[27])],m=!0)},p(M,$){o=M;const k={};1296&$[0]|256&$[1]&&(k.$$scope={dirty:$,ctx:o}),s.$set(k);const B={};256&$[1]&&(B.$$scope={dirty:$,ctx:o}),p.$set(B);const X={};8&$[0]&&(X.title=o[3]===Mt.instruction?"Instruct Augment":"Edit with Augment"),2064&$[0]&&(X.disabled=!o[4].trim()||o[11]),8&$[0]|256&$[1]&&(X.$$scope={dirty:$,ctx:o}),f.$set(X),(!h||32&$[0])&&Tt(n,"top",o[5]+"px"),(!h||64&$[0])&&Tt(n,"height",o[6]+"px")},i(M){h||(y(s.$$.fragment,M),y(p.$$.fragment,M),y(f.$$.fragment,M),h=!0)},o(M){b(s.$$.fragment,M),b(p.$$.fragment,M),b(f.$$.fragment,M),h=!1},d(M){M&&(x(t),x(e),x(n)),o[25](null),S(s),o[26](null),S(p),S(f),m=!1,De(C)}}}function so(o){let t,e,n,i,r,s,a,u;t=new On({props:{shortcuts:{Enter:o[23]}}});let p={requestEditorFocus:o[16],onMentionItemsUpdated:o[18]};return n=new In({props:p}),o[24](n),r=new ji.Content({props:{content:o[4],onContentChanged:o[19]}}),a=new Sn({props:{placeholder:o[10]}}),{c(){A(t.$$.fragment),e=Z(),A(n.$$.fragment),i=Z(),A(r.$$.fragment),s=Z(),A(a.$$.fragment)},m(l,f){N(t,l,f),O(l,e,f),N(n,l,f),O(l,i,f),N(r,l,f),O(l,s,f),N(a,l,f),u=!0},p(l,f){n.$set({});const h={};16&f[0]&&(h.content=l[4]),r.$set(h);const m={};1024&f[0]&&(m.placeholder=l[10]),a.$set(m)},i(l){u||(y(t.$$.fragment,l),y(n.$$.fragment,l),y(r.$$.fragment,l),y(a.$$.fragment,l),u=!0)},o(l){b(t.$$.fragment,l),b(n.$$.fragment,l),b(r.$$.fragment,l),b(a.$$.fragment,l),u=!1},d(l){l&&(x(e),x(i),x(s)),S(t,l),o[24](null),S(n,l),S(r,l),S(a,l)}}}function ao(o){let t,e,n;return e=new Vt({props:{keybinding:"esc"}}),{c(){t=ot(`Close
          `),A(e.$$.fragment)},m(i,r){O(i,t,r),N(e,i,r),n=!0},p:G,i(i){n||(y(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){i&&x(t),S(e,i)}}}function co(o){let t,e=o[3]===Mt.instruction?"Instruct":"Edit";return{c(){t=ot(e)},m(n,i){O(n,t,i)},p(n,i){8&i[0]&&e!==(e=n[3]===Mt.instruction?"Instruct":"Edit")&&je(t,e)},d(n){n&&x(t)}}}function uo(o){let t,e;return t=new ro({props:{slot:"iconRight"}}),{c(){A(t.$$.fragment)},m(n,i){N(t,n,i),e=!0},p:G,i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){b(t.$$.fragment,n),e=!1},d(n){S(t,n)}}}function lo(o){let t,e,n=o[2]&&vi(o);return{c(){n&&n.c(),t=ye()},m(i,r){n&&n.m(i,r),O(i,t,r),e=!0},p(i,r){i[2]?n?(n.p(i,r),4&r[0]&&y(n,1)):(n=vi(i),n.c(),y(n,1),n.m(t.parentNode,t)):n&&(ut(),b(n,1,1,()=>{n=null}),dt())},i(i){e||(y(n),e=!0)},o(i){b(n),e=!1},d(i){i&&x(t),n&&n.d(i)}}}function fo(o,t,e){let n,i,r,s,a,u,p=G,l=()=>(p(),p=_t(h,w=>e(22,s=w)),h),f=G;o.$$.on_destroy.push(()=>p()),o.$$.on_destroy.push(()=>f());let{diffViewModel:h}=t;l();let{initialConversation:m}=t,{initialFlags:C}=t;const I=An.getContext().monaco,M={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},$=new Ai(Xt);let k=new Ni;$.registerConsumer(k);let B=new Si($,Xt,k,{initialConversation:m,initialFlags:C});const X=B.currentConversationModel;let z,et;$.registerConsumer(B),Nn(B);let K,J="";const D=h.mode;Wt(o,D,w=>e(3,a=w));const rt=h.selectionLines;function Et(){const w=h.getModifiedEditor(),Ot=ct(I);if(!w||!Ot||(K==null||K.clear(),!r))return;const Dt=r.start,Ut=r.end,ie={range:new Ot.Range(Dt+1,1,Ut+1,1),options:M};K||(K=w.createDecorationsCollection()),K.set([ie])}function ee(){return!!(J!=null&&J.trim())&&(h.handleInstructionSubmit(J),!0)}Wt(o,rt,w=>e(2,r=w)),Oi(async()=>{await Ae(),tt(),e(5,Ft=h.editorOffset)}),Ii(()=>{z==null||z.destroy(),K==null||K.clear()});let st,j,Ft=0,xt=57;const tt=()=>st==null?void 0:st.forceFocus();return o.$$set=w=>{"diffViewModel"in w&&l(e(0,h=w.diffViewModel)),"initialConversation"in w&&e(20,m=w.initialConversation),"initialFlags"in w&&e(21,C=w.initialFlags)},o.$$.update=()=>{if(8&o.$$.dirty[0]&&e(10,n=(a===Mt.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&o.$$.dirty[0]&&(e(9,i=s.isLoading),f(),f=_t(i,w=>e(11,u=w))),6&o.$$.dirty[0]&&et){if(r==null)e(6,xt=0);else{const w=et.scrollHeight;e(6,xt=Math.min(40+w,108))}z==null||z.update({heightInPx:xt}),Et()}},[h,et,r,a,J,Ft,xt,st,j,i,n,u,D,rt,ee,function(w){if(w){const Ot=r?r.start:1;z=h.renderInstructionsDrawerViewZone(w,{line:Ot,heightInPx:xt,onDomNodeTop:Dt=>{e(5,Ft=h.editorOffset+Dt)},autoFocus:!0}),Et()}},()=>st==null?void 0:st.requestFocus(),tt,w=>{X.saveDraftMentions(w.current)},function(w){e(4,J=w.rawText)},m,C,s,()=>ee(),function(w){Qt[w?"unshift":"push"](()=>{j=w,e(8,j)})},function(w){Qt[w?"unshift":"push"](()=>{st=w,e(7,st)})},function(w){Qt[w?"unshift":"push"](()=>{et=w,e(1,et)})},w=>{w.key==="Enter"&&(tt(),w.stopPropagation(),w.preventDefault())}]}class ho extends Yt{constructor(t){super(),Jt(this,t,fo,lo,te,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Ne}=bn;function bi(o,t,e){const n=o.slice();return n[17]=t[e],n[19]=e,n}function Ci(o){let t,e,n,i,r;return e=new io({props:{diffViewModel:o[3]}}),i=new ho({props:{diffViewModel:o[3]}}),{c(){t=H("div"),A(e.$$.fragment),n=Z(),A(i.$$.fragment),q(t,"class","sticky-top svelte-453n6i")},m(s,a){O(s,t,a),N(e,t,null),O(s,n,a),N(i,s,a),r=!0},p(s,a){const u={};8&a&&(u.diffViewModel=s[3]),e.$set(u);const p={};8&a&&(p.diffViewModel=s[3]),i.$set(p)},i(s){r||(y(e.$$.fragment,s),y(i.$$.fragment,s),r=!0)},o(s){b(e.$$.fragment,s),b(i.$$.fragment,s),r=!1},d(s){s&&(x(t),x(n)),S(e),S(i,s)}}}function ki(o){let t,e,n=li(o[4]),i=[];for(let s=0;s<n.length;s+=1)i[s]=Mi(bi(o,n,s));const r=s=>b(i[s],1,1,()=>{i[s]=null});return{c(){for(let s=0;s<i.length;s+=1)i[s].c();t=ye()},m(s,a){for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(s,a);O(s,t,a),e=!0},p(s,a){if(280&a){let u;for(n=li(s[4]),u=0;u<n.length;u+=1){const p=bi(s,n,u);i[u]?(i[u].p(p,a),y(i[u],1)):(i[u]=Mi(p),i[u].c(),y(i[u],1),i[u].m(t.parentNode,t))}for(ut(),u=n.length;u<i.length;u+=1)r(u);dt()}},i(s){if(!e){for(let a=0;a<n.length;a+=1)y(i[a]);e=!0}},o(s){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)b(i[a]);e=!1},d(s){s&&x(t),$n(i,s)}}}function wi(o){var r;let t,e;function n(){return o[14](o[17])}function i(){return o[15](o[17])}return t=new qn({props:{isFocused:((r=o[3])==null?void 0:r.currFocusedChunkIdx)===o[19],onAccept:n,onReject:i,diffViewModel:o[3],leaf:o[17],align:"right",disableApply:o[8]}}),{c(){A(t.$$.fragment)},m(s,a){N(t,s,a),e=!0},p(s,a){var p;o=s;const u={};8&a&&(u.isFocused=((p=o[3])==null?void 0:p.currFocusedChunkIdx)===o[19]),24&a&&(u.onAccept=n),24&a&&(u.onReject=i),8&a&&(u.diffViewModel=o[3]),16&a&&(u.leaf=o[17]),256&a&&(u.disableApply=o[8]),t.$set(u)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){S(t,s)}}}function Mi(o){let t,e,n=o[17].unitOfCodeWork.modifiedCode!==o[17].unitOfCodeWork.originalCode&&wi(o);return{c(){n&&n.c(),t=ye()},m(i,r){n&&n.m(i,r),O(i,t,r),e=!0},p(i,r){i[17].unitOfCodeWork.modifiedCode!==i[17].unitOfCodeWork.originalCode?n?(n.p(i,r),16&r&&y(n,1)):(n=wi(i),n.c(),y(n,1),n.m(t.parentNode,t)):n&&(ut(),b(n,1,1,()=>{n=null}),dt())},i(i){e||(y(n),e=!0)},o(i){b(n),e=!1},d(i){i&&x(t),n&&n.d(i)}}}function po(o){var p;let t,e,n,i,r,s,a=o[3]&&Ci(o),u=o[3]&&((p=o[4])==null?void 0:p.length)&&!o[7]&&ki(o);return{c(){t=H("div"),a&&a.c(),e=Z(),n=H("div"),i=H("div"),r=Z(),u&&u.c(),q(i,"class","editor svelte-453n6i"),q(n,"class","editor-container svelte-453n6i"),q(t,"class","diff-view-container svelte-453n6i")},m(l,f){O(l,t,f),a&&a.m(t,null),U(t,e),U(t,n),U(n,i),o[13](i),U(n,r),u&&u.m(n,null),s=!0},p(l,f){var h;l[3]?a?(a.p(l,f),8&f&&y(a,1)):(a=Ci(l),a.c(),y(a,1),a.m(t,e)):a&&(ut(),b(a,1,1,()=>{a=null}),dt()),l[3]&&((h=l[4])!=null&&h.length)&&!l[7]?u?(u.p(l,f),152&f&&y(u,1)):(u=ki(l),u.c(),y(u,1),u.m(n,null)):u&&(ut(),b(u,1,1,()=>{u=null}),dt())},i(l){s||(y(a),y(u),s=!0)},o(l){b(a),b(u),s=!1},d(l){l&&x(t),a&&a.d(),o[13](null),u&&u.d()}}}function go(o){let t,e,n,i;return t=new jn.Root({props:{$$slots:{default:[po]},$$scope:{ctx:o}}}),{c(){A(t.$$.fragment)},m(r,s){N(t,r,s),e=!0,n||(i=[kt(Ne,"message",function(){var a,u;$t((a=o[0])==null?void 0:a.handleMessageFromExtension)&&((u=o[0])==null||u.handleMessageFromExtension.apply(this,arguments))}),kt(Ne,"focus",o[11]),kt(Ne,"blur",o[12])],n=!0)},p(r,[s]){o=r;const a={};1048986&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(r){e||(y(t.$$.fragment,r),e=!0)},o(r){b(t.$$.fragment,r),e=!1},d(r){S(t,r),n=!1,De(i)}}}function mo(o,t,e){let n,i,r,s,a,u,p,l,f,h,m=G,C=G,I=G;function M(k){const B=yn.dark;return Ln((k==null?void 0:k.category)||B,k==null?void 0:k.intensity)??Vn.get(B)}Wt(o,_n,k=>e(10,a=k)),o.$$.on_destroy.push(()=>m()),o.$$.on_destroy.push(()=>C()),o.$$.on_destroy.push(()=>I()),Oi(async()=>{e(9,h=await window.augmentDeps.monaco),h||console.error("Monaco not loaded. Diff view cannot be initialized.")}),Ii(()=>{l==null||l.dispose()});let $=!1;return o.$$.update=()=>{if(1539&o.$$.dirty&&h&&f&&!l&&(e(0,l=new Rn(f,M(a),h)),m(),m=_t(l,k=>e(3,s=k))),1&o.$$.dirty&&(e(6,n=l==null?void 0:l.disableApply),I(),I=_t(n,k=>e(8,p=k))),1&o.$$.dirty&&(e(5,i=l==null?void 0:l.disableResolution),C(),C=_t(i,k=>e(7,u=k))),1025&o.$$.dirty){const k=a;l&&(l==null||l.updateTheme(M(k)))}8&o.$$.dirty&&e(4,r=s==null?void 0:s.leaves),5&o.$$.dirty&&(l==null||l.updateIsWebviewFocused($))},[l,f,$,s,r,i,n,u,p,h,a,()=>e(2,$=!0),()=>e(2,$=!1),function(k){Qt[k?"unshift":"push"](()=>{f=k,e(1,f)})},k=>s==null?void 0:s.acceptChunk(k),k=>s==null?void 0:s.rejectChunk(k)]}new class extends Yt{constructor(o){super(),Jt(this,o,mo,go,te,{})}}({target:document.getElementById("app")});
