#!/usr/bin/env python3
"""
Augment Anti-Detection Quick Start
快速启动脚本 - 一键运行反检测
"""

import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    required_packages = ["psutil", "commentjson"]
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    # 检查可选依赖
    optional_packages = {"plyvel": "LevelDB操作（可选）"}
    optional_missing = []
    
    for package, desc in optional_packages.items():
        try:
            __import__(package)
        except ImportError:
            optional_missing.append(f"{package} - {desc}")
    
    if missing:
        print("❌ 缺少必要依赖:")
        for pkg in missing:
            print(f"   - {pkg}")
        print(f"\n📥 安装命令: pip install {' '.join(missing)}")
        return False
    
    if optional_missing:
        print("⚠️ 缺少可选依赖:")
        for pkg in optional_missing:
            print(f"   - {pkg}")
        print("\n💡 建议安装: pip install plyvel")
    
    return True

def main():
    print("🚀 Augment Anti-Detection Quick Start")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 请先安装必要依赖后重试")
        return False
    
    # 运行主脚本
    script_path = Path(__file__).parent / "augment_antidetect_final.py"
    
    if not script_path.exists():
        print(f"❌ 找不到主脚本: {script_path}")
        return False
    
    try:
        print("\n🎯 启动反检测工具...")
        result = subprocess.run([
            sys.executable, 
            str(script_path), 
            "--run"
        ], capture_output=False, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
        sys.exit(1) 