# Augment Anti-Detection Tool

基于完整代码审计的Augment插件反检测工具。

## 核心原理

通过代码审计发现，Augment插件的风控检测主要依赖：

1. **sessionId标识**：存储在VS Code的`state.vscdb`中，用于标识同一IDE实例
2. **网络指纹**：通过HTTP请求头和请求体传输设备信息
3. **遥测数据**：通过analytics库上报使用统计

**LevelDB的作用有限**：主要存储用户偏好和缓存，对风控影响较小。

## 文件结构

```
change/
├── augment_antidetect_final.py  # 主要反检测脚本
├── augment_proxy.py            # 网络代理脚本（运行时生成）
├── start_proxy.sh/bat          # 代理启动脚本（运行时生成）
└── README.md                   # 本说明文件
```

## 使用方法

### 1. 安装依赖

```bash
pip install psutil commentjson plyvel
```

### 2. 运行反检测

```bash
# 完整反检测流程
python augment_antidetect_final.py --run

# 恢复备份
python augment_antidetect_final.py --restore

# 显示帮助
python augment_antidetect_final.py --help
```

### 3. 网络代理模式（高级）

```bash
# 安装mitmproxy
pip install mitmproxy

# 启动代理
cd change/
./start_proxy.sh  # Linux/macOS
# 或
start_proxy.bat   # Windows

# 在新终端中启动VS Code
export http_proxy=http://127.0.0.1:8080
export https_proxy=http://127.0.0.1:8080
code
```

## 操作步骤

1. **备份关键文件**：自动备份所有要修改的文件
2. **修改sessionId**：在VS Code的globalState数据库中更换会话标识
3. **注入JS补丁**：在extension.js中注入反检测代码
4. **观察LevelDB**：分析真实的键名结构（不做修改）
5. **调整UI设置**：随机化一些界面设置
6. **创建网络代理**：生成mitmproxy脚本用于网络层伪装

## 安全特性

- ✅ 完整备份机制，可随时恢复
- ✅ 只修改关键检测点，不影响插件功能
- ✅ 最小化修改原则，降低风险
- ✅ 多层防护：本地+网络层伪装

## 注意事项

1. **关闭VS Code**：运行前建议关闭VS Code避免文件锁定
2. **权限要求**：需要读写VS Code配置文件的权限
3. **备份重要**：所有修改都有备份，建议定期清理旧备份
4. **测试功能**：修改后务必测试Augment插件是否正常工作

## 技术细节

### sessionId位置
- 路径：`~/.config/Code/User/globalStorage/state.vscdb`
- 表：`ItemTable`
- 键：`sessionId`或包含session的复合键

### LevelDB路径
- 真实路径：`globalStorage/Augment.vscode-augment/data/kv-store`
- **不使用kv:前缀**，键名直接如`actionsState`、`featureFlags`等

### JS补丁原理
- 拦截`fetch`函数，修改发往augmentcode.com的请求
- 静默`analytics`遥测函数
- 在请求头和请求体中替换设备指纹

### 网络代理原理
- 使用mitmproxy拦截HTTPS流量
- 递归替换JSON中的设备指纹字段
- 为每个客户端分配一致的伪装配置

## 故障排除

### 常见问题

1. **找不到VS Code目录**
   - 检查是否为标准安装路径
   - 支持Code、Code-Insiders、VSCodium

2. **权限被拒绝**
   - 确保VS Code已关闭
   - Linux/macOS可能需要sudo权限

3. **补丁注入失败**
   - 检查extension.js文件是否存在
   - 文件可能被防病毒软件保护

4. **代理无法启动**
   - 安装mitmproxy：`pip install mitmproxy`
   - 检查端口8080是否被占用

### 恢复原始状态

如果出现问题，可以恢复到修改前的状态：

```bash
python augment_antidetect_final.py --restore
```

或手动恢复：
1. 从备份目录恢复`state.vscdb`
2. 从`.backup`文件恢复`extension.js`
3. 恢复`settings.json`

## 免责声明

本工具仅用于学习和研究目的。使用者应：
- 遵守相关服务条款
- 承担使用风险
- 合法合规使用 