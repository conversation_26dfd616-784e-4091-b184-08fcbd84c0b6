import{p as B}from"./chunk-TMUBEWPD-LmNliwJT.js";import{_ as n,s as F,g as P,o as S,p as W,a as z,b as T,E as u,I as v,e as A,x as D,F as E,G as R,l as $}from"./AugmentMessage-C8cOeLWa.js";import{p as Y}from"./gitGraph-YCYPL57B-D27fxjkY.js";import"./SpinnerAugment-VfHtkDdv.js";import"./IconButtonAugment-BlRCK7lJ.js";import"./CalloutAugment-jvmj3vIU.js";import"./CardAugment-CMpdst0l.js";import"./index-C5qylk65.js";import"./async-messaging-Cm1y2LK7.js";import"./message-broker-DxXjuHCW.js";import"./types-CGlLNakm.js";import"./file-paths-CXmnYUii.js";import"./BaseTextInput-C9A3t790.js";import"./folder-opened-CgcyGshw.js";import"./index-6WVCg-U8.js";import"./diff-operations-DfKvZ1Ug.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-JNVBjzXL.js";import"./index-BsnNYDaF.js";import"./keypress-DD1aQVr0.js";import"./await_block-CntY6A8u.js";import"./OpenFileButton-fgZNybO2.js";import"./chat-context-DhGlDJgc.js";import"./index-B528snJk.js";import"./remote-agents-client-zf3VV9pT.js";import"./ra-diff-ops-model-DMR40nRt.js";import"./TextAreaAugment-BnS2cUNC.js";import"./ButtonAugment-CRJIYorH.js";import"./CollapseButtonAugment-BcgZeyRI.js";import"./partner-mcp-utils-DbWhXw15.js";import"./MaterialIcon-YT2PSBkc.js";import"./CopyButton-BzMAWRcV.js";import"./copy-MzH1hy8q.js";import"./ellipsis-CQoYNkeK.js";import"./IconFilePath-qhm60SDK.js";import"./LanguageIcon-BXmH3Ek-.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-UFj4_Gis.js";import"./index-PzkfeRvH.js";import"./augment-logo-DHqqkJ4i.js";import"./pen-to-square-DiN4Ry3-.js";import"./chevron-down-DQi0HUpw.js";import"./check-ChePEq3H.js";import"./_baseUniq-s71N3Uvp.js";import"./_basePickBy-JEPcOefk.js";import"./clone-Dn8BbtGd.js";var w={packet:[]},x=structuredClone(w),j=R.packet,H=n(()=>{const t=u({...j,...E().packet});return t.showBits&&(t.paddingY+=10),t},"getConfig"),I=n(()=>x.packet,"getPacket"),f={pushWord:n(t=>{t.length>0&&x.packet.push(t)},"pushWord"),getPacket:I,getConfig:H,clear:n(()=>{D(),x=structuredClone(w)},"clear"),setAccTitle:T,getAccTitle:z,setDiagramTitle:W,getDiagramTitle:S,getAccDescription:P,setAccDescription:F},L=n(t=>{B(t,f);let e=-1,a=[],l=1;const{bitsPerRow:s}=f.getConfig();for(let{start:r,end:o,label:d}of t.blocks){if(o&&o<r)throw new Error(`Packet block ${r} - ${o} is invalid. End must be greater than start.`);if(r!==e+1)throw new Error(`Packet block ${r} - ${o??r} is not contiguous. It should start from ${e+1}.`);for(e=o??r,$.debug(`Packet block ${r} - ${e} with label ${d}`);a.length<=s+1&&f.getPacket().length<1e4;){const[g,p]=G({start:r,end:o,label:d},l,s);if(a.push(g),g.end+1===l*s&&(f.pushWord(a),a=[],l++),!p)break;({start:r,end:o,label:d}=p)}}f.pushWord(a)},"populate"),G=n((t,e,a)=>{if(t.end===void 0&&(t.end=t.start),t.start>t.end)throw new Error(`Block start ${t.start} is greater than block end ${t.end}.`);return t.end+1<=e*a?[t,void 0]:[{start:t.start,end:e*a-1,label:t.label},{start:e*a,end:t.end,label:t.label}]},"getNextFittingBlock"),M={parse:n(async t=>{const e=await Y("packet",t);$.debug(e),L(e)},"parse")},N=n((t,e,a,l)=>{const s=l.db,r=s.getConfig(),{rowHeight:o,paddingY:d,bitWidth:g,bitsPerRow:p}=r,h=s.getPacket(),i=s.getDiagramTitle(),m=o+d,c=m*(h.length+1)-(i?0:o),k=g*p+2,b=v(e);b.attr("viewbox",`0 0 ${k} ${c}`),A(b,c,k,r.useMaxWidth);for(const[y,C]of h.entries())X(b,C,y,r);b.append("text").text(i).attr("x",k/2).attr("y",c-m/2).attr("dominant-baseline","middle").attr("text-anchor","middle").attr("class","packetTitle")},"draw"),X=n((t,e,a,{rowHeight:l,paddingX:s,paddingY:r,bitWidth:o,bitsPerRow:d,showBits:g})=>{const p=t.append("g"),h=a*(l+r)+r;for(const i of e){const m=i.start%d*o+1,c=(i.end-i.start+1)*o-s;if(p.append("rect").attr("x",m).attr("y",h).attr("width",c).attr("height",l).attr("class","packetBlock"),p.append("text").attr("x",m+c/2).attr("y",h+l/2).attr("class","packetLabel").attr("dominant-baseline","middle").attr("text-anchor","middle").text(i.label),!g)continue;const k=i.end===i.start,b=h-2;p.append("text").attr("x",m+(k?c/2:0)).attr("y",b).attr("class","packetByte start").attr("dominant-baseline","auto").attr("text-anchor",k?"middle":"start").text(i.start),k||p.append("text").attr("x",m+c).attr("y",b).attr("class","packetByte end").attr("dominant-baseline","auto").attr("text-anchor","end").text(i.end)}},"drawWord"),_={byteFontSize:"10px",startByteColor:"black",endByteColor:"black",labelColor:"black",labelFontSize:"12px",titleColor:"black",titleFontSize:"14px",blockStrokeColor:"black",blockStrokeWidth:"1",blockFillColor:"#efefef"},It={parser:M,db:f,renderer:{draw:N},styles:n(({packet:t}={})=>{const e=u(_,t);return`
	.packetByte {
		font-size: ${e.byteFontSize};
	}
	.packetByte.start {
		fill: ${e.startByteColor};
	}
	.packetByte.end {
		fill: ${e.endByteColor};
	}
	.packetLabel {
		fill: ${e.labelColor};
		font-size: ${e.labelFontSize};
	}
	.packetTitle {
		fill: ${e.titleColor};
		font-size: ${e.titleFontSize};
	}
	.packetBlock {
		stroke: ${e.blockStrokeColor};
		stroke-width: ${e.blockStrokeWidth};
		fill: ${e.blockFillColor};
	}
	`},"styles")};export{It as diagram};
